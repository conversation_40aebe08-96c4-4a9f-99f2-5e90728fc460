<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Triton Quill - Professional Word Processor</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="{{ url_for('static', filename='../assets/TSL-Logo.svg') }}">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #074277;
            --secondary-color: #3A93CC;
            --accent-color: #2871AC;
            --text-dark: #08182B;
            --text-light: #F8F6F3;
            --wave-color-1: #3A93CC;
            --wave-color-2: #2871AC;
            --wave-color-3: #074277;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            height: 100vh;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .splash-container {
            text-align: center;
            color: var(--text-light);
            z-index: 10;
            position: relative;
        }
        
        .logo-container {
            position: relative;
            display: inline-block;
            margin-bottom: 2rem;
        }
        
        .logo {
            width: 200px;
            height: 200px;
            filter: drop-shadow(0 10px 30px rgba(0,0,0,0.3));
            animation: logoFloat 3s ease-in-out infinite;
        }
        
        .logo-waves {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .wave-path {
            animation: waveFlow 4s ease-in-out infinite;
            transform-origin: center;
        }
        
        .wave-path:nth-child(1) {
            animation-delay: 0s;
        }
        
        .wave-path:nth-child(2) {
            animation-delay: 0.5s;
        }
        
        .wave-path:nth-child(3) {
            animation-delay: 1s;
        }
        
        .title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.5s forwards;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.5rem;
            font-weight: 300;
            margin-bottom: 3rem;
            opacity: 0;
            animation: fadeInUp 1s ease-out 1s forwards;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        
        .loading-text {
            font-size: 1.2rem;
            font-weight: 400;
            opacity: 0;
            animation: fadeInUp 1s ease-out 1.5s forwards;
            margin-bottom: 1rem;
        }
        
        .loading-dots {
            display: inline-block;
            opacity: 0;
            animation: fadeInUp 1s ease-out 2s forwards;
        }
        
        .loading-dots span {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: var(--text-light);
            margin: 0 3px;
            animation: loadingDots 1.5s ease-in-out infinite;
        }
        
        .loading-dots span:nth-child(1) { animation-delay: 0s; }
        .loading-dots span:nth-child(2) { animation-delay: 0.2s; }
        .loading-dots span:nth-child(3) { animation-delay: 0.4s; }
        
        .get-started-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid var(--text-light);
            color: var(--text-light);
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            opacity: 0;
            animation: fadeInUp 1s ease-out 3s forwards;
            backdrop-filter: blur(10px);
        }
        
        .get-started-btn:hover {
            background: var(--text-light);
            color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        /* Background animated waves */
        .bg-waves {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }
        
        .bg-wave {
            position: absolute;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, 
                rgba(58, 147, 204, 0.1) 0%, 
                rgba(40, 113, 172, 0.1) 50%, 
                rgba(7, 66, 119, 0.1) 100%);
            border-radius: 45%;
            animation: bgWaveRotate 20s linear infinite;
        }
        
        .bg-wave:nth-child(1) {
            top: -50%;
            left: -50%;
            animation-duration: 25s;
        }
        
        .bg-wave:nth-child(2) {
            top: -60%;
            right: -50%;
            animation-duration: 30s;
            animation-direction: reverse;
        }
        
        .bg-wave:nth-child(3) {
            bottom: -50%;
            left: -60%;
            animation-duration: 35s;
        }
        
        /* Animations */
        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        @keyframes waveFlow {
            0%, 100% { 
                transform: scale(1) rotate(0deg);
                opacity: 0.8;
            }
            50% { 
                transform: scale(1.05) rotate(2deg);
                opacity: 1;
            }
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes loadingDots {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1.2);
                opacity: 1;
            }
        }
        
        @keyframes bgWaveRotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Transition out animation */
        .splash-container.fade-out {
            animation: fadeOut 1s ease-in forwards;
        }
        
        @keyframes fadeOut {
            to {
                opacity: 0;
                transform: scale(0.9);
            }
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            .title {
                font-size: 2.5rem;
            }
            
            .subtitle {
                font-size: 1.2rem;
            }
            
            .logo {
                width: 150px;
                height: 150px;
            }
            
            .get-started-btn {
                padding: 12px 30px;
                font-size: 1rem;
            }
        }
        
        @media (max-width: 480px) {
            .title {
                font-size: 2rem;
            }
            
            .subtitle {
                font-size: 1rem;
            }
            
            .logo {
                width: 120px;
                height: 120px;
            }
        }
    </style>
</head>
<body>
    <!-- Background animated waves -->
    <div class="bg-waves">
        <div class="bg-wave"></div>
        <div class="bg-wave"></div>
        <div class="bg-wave"></div>
    </div>
    
    <!-- Main splash content -->
    <div class="splash-container" id="splashContainer">
        <div class="logo-container">
            <svg class="logo" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                <!-- Base logo paths from TSL-Logo.svg -->
                <path d="M0 0 C337.92 0 675.84 0 1024 0 C1024 337.92 1024 675.84 1024 1024 C686.08 1024 348.16 1024 0 1024 C0 686.08 0 348.16 0 0 Z" fill="#08182B"/>
                
                <!-- Animated wave paths -->
                <g class="logo-waves">
                    <path class="wave-path" d="M0 0 C2.72827375 2.21104675 5.37257504 4.49604635 8.01171875 6.8125 C9.51960068 8.0581416 11.02753231 9.30372438 12.53686523 10.54760742 C20.72150619 17.33837392 28.38139297 24.67770603 36.09326172 31.99414062 C66.98134872 61.27060689 102.50494149 85.01997829 146.38671875 85.0625 C147.46335281 85.06401062 147.46335281 85.06401062 148.56173706 85.06555176 C159.34654264 85.05530477 169.51655912 84.49235379 180.01171875 81.8125 C180.71683594 81.63605957 181.42195313 81.45961914 182.1484375 81.27783203 C186.45737206 80.19392526 190.74032251 79.03733997 195.01171875 77.8125 C193.5817873 82.30774428 191.08760478 85.27547438 187.88671875 88.625 C187.39026855 89.15198486 186.89381836 89.67896973 186.38232422 90.22192383 C185.38524494 91.27650832 184.38481772 92.32793879 183.38085938 93.37597656 C182.2263457 94.5873101 181.09305658 95.81882077 179.96484375 97.0546875 C160.88039038 117.36250326 131.57726343 128.83330884 104.04296875 130.0859375 C57.53502087 131.30494684 21.1365776 106.55630037 -12.87109375 77.46484375 C-16.592613 74.28494869 -20.39176732 71.20302143 -24.19140625 68.1171875 C-26.15980463 66.49518324 -28.10503249 64.86135249 -30.03515625 63.1953125 C-54.79612946 41.83215649 -82.79968053 23.53623188 -116.83300781 25.63232422 C-129.45936766 26.68785815 -141.13505515 30.57332539 -152.98828125 34.8125 C-151.62362265 29.87123618 -149.37481163 26.08413024 -146.36328125 22 C-145.91525146 21.38616455 -145.46722168 20.7723291 -145.00561523 20.13989258 C-129.15952018 -1.31419272 -106.28335105 -16.73554947 -79.64306641 -20.87841797 C-50.81492755 -24.23471959 -23.41657957 -17.14154844 0 0 Z" fill="var(--wave-color-1)" transform="translate(393.98828125,333.1875)"/>
                    
                    <path class="wave-path" d="M0 0 C2.72827375 2.21104675 5.37257504 4.49604635 8.01171875 6.8125 C9.51960068 8.0581416 11.02753231 9.30372438 12.53686523 10.54760742 C20.72150619 17.33837392 28.38139297 24.67770603 36.09326172 31.99414062 C66.98134872 61.27060689 102.50494149 85.01997829 146.38671875 85.0625 C147.46335281 85.06401062 147.46335281 85.06401062 148.56173706 85.06555176 C159.34654264 85.05530477 169.51655912 84.49235379 180.01171875 81.8125 C180.71683594 81.63605957 181.42195313 81.45961914 182.1484375 81.27783203 C186.45737206 80.19392526 190.74032251 79.03733997 195.01171875 77.8125 C193.5817873 82.30774428 191.08760478 85.27547438 187.88671875 88.625 C187.39026855 89.15198486 186.89381836 89.67896973 186.38232422 90.22192383 C185.38524494 91.27650832 184.38481772 92.32793879 183.38085938 93.37597656 C182.2263457 94.5873101 181.09305658 95.81882077 179.96484375 97.0546875 C160.88039038 117.36250326 131.57726343 128.83330884 104.04296875 130.0859375 C57.53502087 131.30494684 21.1365776 106.55630037 -12.87109375 77.46484375 C-16.592613 74.28494869 -20.39176732 71.20302143 -24.19140625 68.1171875 C-26.15980463 66.49518324 -28.10503249 64.86135249 -30.03515625 63.1953125 C-54.79612946 41.83215649 -82.79968053 23.53623188 -116.83300781 25.63232422 C-129.45936766 26.68785815 -141.13505515 30.57332539 -152.98828125 34.8125 C-151.62362265 29.87123618 -149.37481163 26.08413024 -146.36328125 22 C-145.91525146 21.38616455 -145.46722168 20.7723291 -145.00561523 20.13989258 C-129.15952018 -1.31419272 -106.28335105 -16.73554947 -79.64306641 -20.87841797 C-50.81492755 -24.23471959 -23.41657957 -17.14154844 0 0 Z" fill="var(--wave-color-2)" transform="translate(463.17578125,263.76953125)"/>
                    
                    <path class="wave-path" d="M0 0 C1.28266094 4.29762439 0.56302391 8.00906081 -0.1796875 12.3515625 C-0.31190704 13.16563568 -0.44412659 13.97970886 -0.58035278 14.81845093 C-1.00437408 17.42193485 -1.43908579 20.02348328 -1.875 22.625 C-6.55533849 50.90314931 -10.76811811 83.13970325 -2 111 C-1.7745752 111.74910645 -1.54915039 112.49821289 -1.31689453 113.27001953 C5.55331 135.50373644 18.2605765 155.37821247 30.28613281 175.11035156 C47.8804303 204.2372821 53.22477547 238.32512662 45.11987305 271.5246582 C40.02152388 290.74419616 31.08084277 308.9493812 18 324 C17.53335938 324.53818359 17.06671875 325.07636719 16.5859375 325.63085938 C2.72199406 341.4409446 -13.00814448 355.64226662 -32 365 C-30.68246368 362.183519 -29.2060487 359.81821388 -27.27734375 357.3828125 C-26.74326904 356.7007373 -26.20919434 356.01866211 -25.65893555 355.31591797 C-25.09086182 354.59291504 -24.52278809 353.86991211 -23.9375 353.125 C-2.52706859 325.44586829 10.49564594 297.43169194 6.74365234 261.54833984 C3.08505265 237.61660001 -13.31903165 216.40767569 -28 198 C-49.79758666 170.64308648 -64.9312652 144.96444241 -61.79785156 108.65771484 C-56.78047755 66.44550593 -29.67277688 28.87080993 0 0 Z" fill="var(--wave-color-3)" transform="translate(638,86)"/>
                </g>
                
                <!-- Rest of the logo paths -->
                <path d="M0 0 C61.05 0 122.1 0 185 0 C185 13.2 185 26.4 185 40 C161.94772055 40.87818207 139.10454237 41 116 41 C116 94.13 116 147.26 116 202 C101.15 202 86.3 202 71 202 C71 148.87 71 95.74 71 41 C47.57 41 24.14 41 0 41 C0 27.47 0 13.94 0 0 Z" fill="#F7F5F2" transform="translate(221,515)"/>
                <path d="M0 0 C15.18 0 30.36 0 46 0 C46 53.79 46 107.58 46 163 C81.31 163 116.62 163 153 163 C153 175.87 153 188.74 153 202 C102.51 202 52.02 202 0 202 C0 135.34 0 68.68 0 0 Z" fill="#F8F6F3" transform="translate(637,515)"/>
            </svg>
        </div>
        
        <h1 class="title">Triton Quill</h1>
        <p class="subtitle">Professional Word Processing Made Simple</p>
        
        <div class="loading-text">Initializing your workspace</div>
        <div class="loading-dots">
            <span></span>
            <span></span>
            <span></span>
        </div>
        
        <div style="margin-top: 2rem;">
            <a href="{{ url_for('auth.login') }}" class="get-started-btn" id="getStartedBtn">
                Get Started
            </a>
        </div>
    </div>
    
    <script>
        // Auto-redirect after animation completes
        setTimeout(() => {
            const container = document.getElementById('splashContainer');
            const btn = document.getElementById('getStartedBtn');
            
            // Show the get started button
            btn.style.display = 'inline-block';
            
            // Optional: Auto-redirect after showing button for a few seconds
            // setTimeout(() => {
            //     window.location.href = "{{ url_for('auth.login') }}";
            // }, 2000);
        }, 3000);
        
        // Smooth transition when clicking get started
        document.getElementById('getStartedBtn').addEventListener('click', function(e) {
            e.preventDefault();
            
            const container = document.getElementById('splashContainer');
            container.classList.add('fade-out');
            
            setTimeout(() => {
                window.location.href = this.href;
            }, 1000);
        });
        
        // Add some interactive wave effects on mouse move
        document.addEventListener('mousemove', function(e) {
            const waves = document.querySelectorAll('.wave-path');
            const mouseX = e.clientX / window.innerWidth;
            const mouseY = e.clientY / window.innerHeight;
            
            waves.forEach((wave, index) => {
                const intensity = (index + 1) * 0.5;
                const offsetX = (mouseX - 0.5) * intensity;
                const offsetY = (mouseY - 0.5) * intensity;
                
                wave.style.transform = `translate(${offsetX}px, ${offsetY}px) scale(${1 + intensity * 0.02})`;
            });
        });
    </script>
</body>
</html>
