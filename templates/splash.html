<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Triton Quill - Professional Word Processor</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="{{ url_for('serve_assets', filename='TSL-Logo.svg') }}">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #074277;
            --secondary-color: #3A93CC;
            --accent-color: #2871AC;
            --text-dark: #08182B;
            --text-light: #F8F6F3;
            --wave-color-1: #3A93CC;
            --wave-color-2: #2871AC;
            --wave-color-3: #074277;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            height: 100vh;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .splash-container {
            text-align: center;
            color: var(--text-light);
            z-index: 10;
            position: relative;
        }
        
        .logo-container {
            position: relative;
            display: inline-block;
            margin-bottom: 2rem;
        }
        
        .logo {
            width: 200px;
            height: 200px;
            filter: drop-shadow(0 10px 30px rgba(0,0,0,0.3));
            animation: logoFloat 3s ease-in-out infinite;
        }
        
        .logo-waves {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .wave-path {
            animation: waveFlow 4s ease-in-out infinite;
            transform-origin: center;
        }
        
        .wave-path:nth-child(1) {
            animation-delay: 0s;
        }
        
        .wave-path:nth-child(2) {
            animation-delay: 0.5s;
        }
        
        .wave-path:nth-child(3) {
            animation-delay: 1s;
        }
        
        .title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.5s forwards;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.5rem;
            font-weight: 300;
            margin-bottom: 3rem;
            opacity: 0;
            animation: fadeInUp 1s ease-out 1s forwards;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        
        .loading-text {
            font-size: 1.2rem;
            font-weight: 400;
            opacity: 0;
            animation: fadeInUp 1s ease-out 1.5s forwards;
            margin-bottom: 1rem;
        }
        
        .loading-dots {
            display: inline-block;
            opacity: 0;
            animation: fadeInUp 1s ease-out 2s forwards;
        }
        
        .loading-dots span {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: var(--text-light);
            margin: 0 3px;
            animation: loadingDots 1.5s ease-in-out infinite;
        }
        
        .loading-dots span:nth-child(1) { animation-delay: 0s; }
        .loading-dots span:nth-child(2) { animation-delay: 0.2s; }
        .loading-dots span:nth-child(3) { animation-delay: 0.4s; }
        
        .get-started-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid var(--text-light);
            color: var(--text-light);
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            opacity: 0;
            animation: fadeInUp 1s ease-out 3s forwards;
            backdrop-filter: blur(10px);
        }
        
        .get-started-btn:hover {
            background: var(--text-light);
            color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        /* Background animated waves */
        .bg-waves {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }
        
        .bg-wave {
            position: absolute;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, 
                rgba(58, 147, 204, 0.1) 0%, 
                rgba(40, 113, 172, 0.1) 50%, 
                rgba(7, 66, 119, 0.1) 100%);
            border-radius: 45%;
            animation: bgWaveRotate 20s linear infinite;
        }
        
        .bg-wave:nth-child(1) {
            top: -50%;
            left: -50%;
            animation-duration: 25s;
        }
        
        .bg-wave:nth-child(2) {
            top: -60%;
            right: -50%;
            animation-duration: 30s;
            animation-direction: reverse;
        }
        
        .bg-wave:nth-child(3) {
            bottom: -50%;
            left: -60%;
            animation-duration: 35s;
        }
        
        /* Animations */
        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        @keyframes waveFlow {
            0%, 100% { 
                transform: scale(1) rotate(0deg);
                opacity: 0.8;
            }
            50% { 
                transform: scale(1.05) rotate(2deg);
                opacity: 1;
            }
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes loadingDots {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1.2);
                opacity: 1;
            }
        }
        
        @keyframes bgWaveRotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Transition out animation */
        .splash-container.fade-out {
            animation: fadeOut 1s ease-in forwards;
        }
        
        @keyframes fadeOut {
            to {
                opacity: 0;
                transform: scale(0.9);
            }
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            .title {
                font-size: 2.5rem;
            }
            
            .subtitle {
                font-size: 1.2rem;
            }
            
            .logo {
                width: 150px;
                height: 150px;
            }
            
            .get-started-btn {
                padding: 12px 30px;
                font-size: 1rem;
            }
        }
        
        @media (max-width: 480px) {
            .title {
                font-size: 2rem;
            }
            
            .subtitle {
                font-size: 1rem;
            }
            
            .logo {
                width: 120px;
                height: 120px;
            }
        }
    </style>
</head>
<body>
    <!-- Background animated waves -->
    <div class="bg-waves">
        <div class="bg-wave"></div>
        <div class="bg-wave"></div>
        <div class="bg-wave"></div>
    </div>
    
    <!-- Main splash content -->
    <div class="splash-container" id="splashContainer">
        <div class="logo-container">
            <img src="{{ url_for('serve_assets', filename='TSL-Logo.svg') }}" alt="Triton Software Labs" class="logo">
        </div>
        
        <h1 class="title">Triton Quill</h1>
        <p class="subtitle">Professional Word Processing Made Simple</p>
        
        <div class="loading-text">Initializing your workspace</div>
        <div class="loading-dots">
            <span></span>
            <span></span>
            <span></span>
        </div>
        
        <div style="margin-top: 2rem;">
            <a href="{{ url_for('auth.login') }}" class="get-started-btn" id="getStartedBtn">
                Get Started
            </a>
        </div>
    </div>
    
    <script>
        // Auto-redirect after animation completes
        setTimeout(() => {
            const container = document.getElementById('splashContainer');
            const btn = document.getElementById('getStartedBtn');
            
            // Show the get started button
            btn.style.display = 'inline-block';
            
            // Optional: Auto-redirect after showing button for a few seconds
            // setTimeout(() => {
            //     window.location.href = "{{ url_for('auth.login') }}";
            // }, 2000);
        }, 3000);
        
        // Smooth transition when clicking get started
        document.getElementById('getStartedBtn').addEventListener('click', function(e) {
            e.preventDefault();
            
            const container = document.getElementById('splashContainer');
            container.classList.add('fade-out');
            
            setTimeout(() => {
                window.location.href = this.href;
            }, 1000);
        });
        
        // Add some interactive wave effects on mouse move
        document.addEventListener('mousemove', function(e) {
            const waves = document.querySelectorAll('.wave-path');
            const mouseX = e.clientX / window.innerWidth;
            const mouseY = e.clientY / window.innerHeight;
            
            waves.forEach((wave, index) => {
                const intensity = (index + 1) * 0.5;
                const offsetX = (mouseX - 0.5) * intensity;
                const offsetY = (mouseY - 0.5) * intensity;
                
                wave.style.transform = `translate(${offsetX}px, ${offsetY}px) scale(${1 + intensity * 0.02})`;
            });
        });
    </script>
</body>
</html>
