{% extends "base.html" %}

{% block title %}{% if document %}{{ document.title }} - {% endif %}Editor - <PERSON><PERSON>{% endblock %}

{% block styles %}
<style>
    body {
        background: #f8f9fa;
    }
    
    .editor-container {
        height: calc(100vh - 76px);
        display: flex;
        flex-direction: column;
    }
    
    .editor-toolbar {
        background: white;
        border-bottom: 1px solid var(--border-color);
        padding: 0.75rem 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        flex-wrap: wrap;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    
    .toolbar-group {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        padding: 0 0.5rem;
        border-right: 1px solid var(--border-color);
    }
    
    .toolbar-group:last-child {
        border-right: none;
    }
    
    .toolbar-btn {
        background: none;
        border: 1px solid transparent;
        padding: 0.5rem;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
        color: var(--text-dark);
        font-size: 0.9rem;
        min-width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .toolbar-btn:hover {
        background: var(--background-light);
        border-color: var(--border-color);
    }
    
    .toolbar-btn.active {
        background: var(--secondary-color);
        color: white;
        border-color: var(--secondary-color);
    }
    
    .toolbar-select {
        border: 1px solid var(--border-color);
        border-radius: 6px;
        padding: 0.4rem 0.6rem;
        background: white;
        font-size: 0.9rem;
        min-width: 120px;
    }
    
    .toolbar-select:focus {
        outline: none;
        border-color: var(--secondary-color);
        box-shadow: 0 0 0 2px rgba(58, 147, 204, 0.2);
    }
    
    .document-title-input {
        border: none;
        background: none;
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-dark);
        padding: 0.5rem;
        border-radius: 6px;
        transition: all 0.2s ease;
        min-width: 200px;
    }
    
    .document-title-input:focus {
        outline: none;
        background: var(--background-light);
    }
    
    .editor-main {
        flex: 1;
        display: flex;
        overflow: hidden;
    }
    
    .editor-sidebar {
        width: 300px;
        background: white;
        border-right: 1px solid var(--border-color);
        display: flex;
        flex-direction: column;
        transition: all 0.3s ease;
    }
    
    .editor-sidebar.collapsed {
        width: 0;
        overflow: hidden;
    }
    
    .sidebar-tabs {
        display: flex;
        border-bottom: 1px solid var(--border-color);
    }
    
    .sidebar-tab {
        flex: 1;
        padding: 0.75rem;
        text-align: center;
        cursor: pointer;
        border-bottom: 2px solid transparent;
        transition: all 0.2s ease;
        font-size: 0.9rem;
        font-weight: 500;
    }
    
    .sidebar-tab.active {
        border-bottom-color: var(--secondary-color);
        color: var(--secondary-color);
        background: var(--background-light);
    }
    
    .sidebar-content {
        flex: 1;
        padding: 1rem;
        overflow-y: auto;
    }
    
    .editor-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        background: white;
        margin: 1rem;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        overflow: hidden;
    }
    
    .document-header {
        padding: 1.5rem 2rem 1rem;
        border-bottom: 1px solid var(--border-color);
        background: var(--background-light);
    }
    
    .document-stats {
        display: flex;
        gap: 2rem;
        font-size: 0.9rem;
        color: #666;
        margin-top: 0.5rem;
    }
    
    .editor-wrapper {
        flex: 1;
        position: relative;
        overflow: hidden;
    }
    
    #editor {
        height: 100%;
        border: none;
    }
    
    .ql-toolbar {
        display: none !important;
    }
    
    .ql-container {
        border: none !important;
        font-size: 16px;
        line-height: 1.6;
    }
    
    .ql-editor {
        padding: 2rem 3rem;
        min-height: calc(100vh - 300px);
        font-family: 'Times New Roman', serif;
        color: var(--text-dark);
    }
    
    .ql-editor:focus {
        outline: none;
    }
    
    .collaboration-cursors {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
        z-index: 10;
    }
    
    .user-cursor {
        position: absolute;
        width: 2px;
        height: 20px;
        background: var(--secondary-color);
        animation: blink 1s infinite;
    }
    
    .user-cursor::after {
        content: attr(data-user);
        position: absolute;
        top: -25px;
        left: 0;
        background: var(--secondary-color);
        color: white;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 0.75rem;
        white-space: nowrap;
    }
    
    .comments-panel {
        width: 350px;
        background: white;
        border-left: 1px solid var(--border-color);
        display: flex;
        flex-direction: column;
        transition: all 0.3s ease;
    }
    
    .comments-panel.hidden {
        width: 0;
        overflow: hidden;
    }
    
    .comments-header {
        padding: 1rem;
        border-bottom: 1px solid var(--border-color);
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: between;
    }
    
    .comments-list {
        flex: 1;
        overflow-y: auto;
        padding: 1rem;
    }
    
    .comment-item {
        background: var(--background-light);
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        border-left: 3px solid var(--secondary-color);
    }
    
    .comment-author {
        font-weight: 600;
        font-size: 0.9rem;
        color: var(--text-dark);
        margin-bottom: 0.25rem;
    }
    
    .comment-time {
        font-size: 0.8rem;
        color: #666;
        margin-bottom: 0.5rem;
    }
    
    .comment-text {
        font-size: 0.9rem;
        line-height: 1.4;
        color: var(--text-dark);
    }
    
    .floating-toolbar {
        position: absolute;
        background: var(--text-dark);
        color: white;
        border-radius: 8px;
        padding: 0.5rem;
        display: none;
        z-index: 1000;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    }
    
    .floating-toolbar button {
        background: none;
        border: none;
        color: white;
        padding: 0.4rem;
        border-radius: 4px;
        cursor: pointer;
        margin: 0 0.1rem;
        transition: background 0.2s ease;
    }
    
    .floating-toolbar button:hover {
        background: rgba(255,255,255,0.2);
    }
    
    .save-indicator {
        position: fixed;
        top: 100px;
        right: 20px;
        background: var(--success-color);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.9rem;
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: 1000;
    }
    
    .save-indicator.show {
        opacity: 1;
    }
    
    .word-count {
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.9rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: 1px solid var(--border-color);
    }
    
    @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0; }
    }
    
    @media (max-width: 768px) {
        .editor-sidebar {
            position: absolute;
            left: -300px;
            height: 100%;
            z-index: 100;
        }
        
        .editor-sidebar.show {
            left: 0;
        }
        
        .comments-panel {
            position: absolute;
            right: -350px;
            height: 100%;
            z-index: 100;
        }
        
        .comments-panel.show {
            right: 0;
        }
        
        .ql-editor {
            padding: 1rem;
        }
        
        .toolbar-group {
            padding: 0 0.25rem;
        }
        
        .document-title-input {
            min-width: 150px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="editor-container">
    <!-- Toolbar -->
    <div class="editor-toolbar">
        <!-- File operations -->
        <div class="toolbar-group">
            <button class="toolbar-btn" onclick="saveDocument()" title="Save (Ctrl+S)">
                <i class="fas fa-save"></i>
            </button>
            <button class="toolbar-btn" onclick="exportDocument('pdf')" title="Export as PDF">
                <i class="fas fa-file-pdf"></i>
            </button>
            <button class="toolbar-btn" onclick="exportDocument('docx')" title="Export as Word">
                <i class="fas fa-file-word"></i>
            </button>
        </div>
        
        <!-- Document title -->
        <div class="toolbar-group">
            <input type="text" class="document-title-input" id="documentTitle" 
                   value="{% if document %}{{ document.title }}{% else %}Untitled Document{% endif %}"
                   placeholder="Document title">
        </div>
        
        <!-- Text formatting -->
        <div class="toolbar-group">
            <button class="toolbar-btn" onclick="formatText('bold')" title="Bold (Ctrl+B)">
                <i class="fas fa-bold"></i>
            </button>
            <button class="toolbar-btn" onclick="formatText('italic')" title="Italic (Ctrl+I)">
                <i class="fas fa-italic"></i>
            </button>
            <button class="toolbar-btn" onclick="formatText('underline')" title="Underline (Ctrl+U)">
                <i class="fas fa-underline"></i>
            </button>
            <button class="toolbar-btn" onclick="formatText('strike')" title="Strikethrough">
                <i class="fas fa-strikethrough"></i>
            </button>
        </div>
        
        <!-- Font and size -->
        <div class="toolbar-group">
            <select class="toolbar-select" id="fontFamily" onchange="changeFontFamily(this.value)">
                <option value="Arial">Arial</option>
                <option value="Times New Roman" selected>Times New Roman</option>
                <option value="Helvetica">Helvetica</option>
                <option value="Georgia">Georgia</option>
                <option value="Verdana">Verdana</option>
                <option value="Courier New">Courier New</option>
            </select>
            <select class="toolbar-select" id="fontSize" onchange="changeFontSize(this.value)">
                <option value="10">10</option>
                <option value="11">11</option>
                <option value="12" selected>12</option>
                <option value="14">14</option>
                <option value="16">16</option>
                <option value="18">18</option>
                <option value="20">20</option>
                <option value="24">24</option>
                <option value="28">28</option>
                <option value="32">32</option>
            </select>
        </div>
        
        <!-- Alignment -->
        <div class="toolbar-group">
            <button class="toolbar-btn" onclick="alignText('left')" title="Align Left">
                <i class="fas fa-align-left"></i>
            </button>
            <button class="toolbar-btn" onclick="alignText('center')" title="Align Center">
                <i class="fas fa-align-center"></i>
            </button>
            <button class="toolbar-btn" onclick="alignText('right')" title="Align Right">
                <i class="fas fa-align-right"></i>
            </button>
            <button class="toolbar-btn" onclick="alignText('justify')" title="Justify">
                <i class="fas fa-align-justify"></i>
            </button>
        </div>
        
        <!-- Lists -->
        <div class="toolbar-group">
            <button class="toolbar-btn" onclick="insertList('ordered')" title="Numbered List">
                <i class="fas fa-list-ol"></i>
            </button>
            <button class="toolbar-btn" onclick="insertList('bullet')" title="Bullet List">
                <i class="fas fa-list-ul"></i>
            </button>
        </div>
        
        <!-- Insert -->
        <div class="toolbar-group">
            <button class="toolbar-btn" onclick="insertTable()" title="Insert Table">
                <i class="fas fa-table"></i>
            </button>
            <button class="toolbar-btn" onclick="insertImage()" title="Insert Image">
                <i class="fas fa-image"></i>
            </button>
            <button class="toolbar-btn" onclick="insertLink()" title="Insert Link">
                <i class="fas fa-link"></i>
            </button>
        </div>
        
        <!-- Tools -->
        <div class="toolbar-group">
            <button class="toolbar-btn" onclick="findReplace()" title="Find & Replace (Ctrl+F)">
                <i class="fas fa-search"></i>
            </button>
            <button class="toolbar-btn" onclick="spellCheck()" title="Spell Check">
                <i class="fas fa-spell-check"></i>
            </button>
        </div>
        
        <!-- View -->
        <div class="toolbar-group">
            <button class="toolbar-btn" onclick="toggleSidebar()" title="Toggle Sidebar">
                <i class="fas fa-bars"></i>
            </button>
            <button class="toolbar-btn" onclick="toggleComments()" title="Comments">
                <i class="fas fa-comments"></i>
            </button>
            <button class="toolbar-btn" onclick="toggleFullscreen()" title="Fullscreen">
                <i class="fas fa-expand"></i>
            </button>
        </div>
    </div>
    
    <!-- Main editor area -->
    <div class="editor-main">
        <!-- Sidebar -->
        <div class="editor-sidebar" id="editorSidebar">
            <div class="sidebar-tabs">
                <div class="sidebar-tab active" onclick="switchSidebarTab('outline')">Outline</div>
                <div class="sidebar-tab" onclick="switchSidebarTab('styles')">Styles</div>
                <div class="sidebar-tab" onclick="switchSidebarTab('history')">History</div>
            </div>
            
            <div class="sidebar-content">
                <div id="outlineTab" class="sidebar-tab-content">
                    <h6>Document Outline</h6>
                    <div id="documentOutline">
                        <p class="text-muted">Start writing to see your document outline here.</p>
                    </div>
                </div>
                
                <div id="stylesTab" class="sidebar-tab-content" style="display: none;">
                    <h6>Paragraph Styles</h6>
                    <div class="style-options">
                        <button class="btn btn-sm btn-outline-primary mb-2 w-100" onclick="applyStyle('heading1')">
                            Heading 1
                        </button>
                        <button class="btn btn-sm btn-outline-primary mb-2 w-100" onclick="applyStyle('heading2')">
                            Heading 2
                        </button>
                        <button class="btn btn-sm btn-outline-primary mb-2 w-100" onclick="applyStyle('heading3')">
                            Heading 3
                        </button>
                        <button class="btn btn-sm btn-outline-secondary mb-2 w-100" onclick="applyStyle('normal')">
                            Normal Text
                        </button>
                        <button class="btn btn-sm btn-outline-secondary mb-2 w-100" onclick="applyStyle('quote')">
                            Quote
                        </button>
                    </div>
                </div>
                
                <div id="historyTab" class="sidebar-tab-content" style="display: none;">
                    <h6>Version History</h6>
                    <div id="versionHistory">
                        <p class="text-muted">Document versions will appear here.</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Editor content -->
        <div class="editor-content">
            <div class="document-header">
                <div class="document-stats">
                    <span id="wordCount">0 words</span>
                    <span id="charCount">0 characters</span>
                    <span id="lastSaved">Never saved</span>
                </div>
            </div>
            
            <div class="editor-wrapper">
                <div id="editor"></div>
                <div class="collaboration-cursors" id="collaborationCursors"></div>
            </div>
        </div>
        
        <!-- Comments panel -->
        <div class="comments-panel hidden" id="commentsPanel">
            <div class="comments-header">
                <span>Comments</span>
                <button class="btn btn-sm btn-outline-secondary" onclick="toggleComments()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="comments-list" id="commentsList">
                <p class="text-muted">No comments yet. Select text and add a comment to get started.</p>
            </div>
        </div>
    </div>
</div>

<!-- Floating toolbar for text selection -->
<div class="floating-toolbar" id="floatingToolbar">
    <button onclick="formatText('bold')" title="Bold"><i class="fas fa-bold"></i></button>
    <button onclick="formatText('italic')" title="Italic"><i class="fas fa-italic"></i></button>
    <button onclick="formatText('underline')" title="Underline"><i class="fas fa-underline"></i></button>
    <button onclick="addComment()" title="Add Comment"><i class="fas fa-comment"></i></button>
</div>

<!-- Save indicator -->
<div class="save-indicator" id="saveIndicator">
    <i class="fas fa-check me-2"></i>Saved
</div>

<!-- Word count display -->
<div class="word-count" id="wordCountDisplay">
    0 words
</div>

<!-- Hidden file input for image upload -->
<input type="file" id="imageUpload" accept="image/*" style="display: none;" onchange="handleImageUpload(this)">

<script>
    // Global variables
    let quill;
    let documentId = {% if document %}{{ document.id }}{% else %}null{% endif %};
    let socket;
    let saveTimeout;
    let lastSaveTime = null;
    
    // Initialize editor
    document.addEventListener('DOMContentLoaded', function() {
        initializeEditor();
        initializeSocketIO();
        setupKeyboardShortcuts();
        loadDocument();
    });
</script>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/editor.js') }}"></script>
{% endblock %}
