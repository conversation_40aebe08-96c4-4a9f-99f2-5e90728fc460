{% extends "base.html" %}

{% block title %}Home - Triton Quill{% endblock %}

{% block styles %}
<style>
    .hero-section {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 4rem 0;
        margin-bottom: 3rem;
        position: relative;
        overflow: hidden;
    }
    
    .hero-section::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: float 8s ease-in-out infinite;
    }
    
    .hero-content {
        position: relative;
        z-index: 2;
    }
    
    .welcome-title {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-shadow: 0 4px 20px rgba(0,0,0,0.3);
    }
    
    .welcome-subtitle {
        font-size: 1.3rem;
        font-weight: 300;
        opacity: 0.9;
        margin-bottom: 2rem;
    }
    
    .stats-row {
        margin-top: 2rem;
    }
    
    .stat-item {
        text-align: center;
        padding: 1rem;
    }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        display: block;
    }
    
    .stat-label {
        font-size: 1rem;
        opacity: 0.8;
    }
    
    .quick-actions {
        margin-bottom: 3rem;
    }
    
    .action-card {
        background: white;
        border-radius: 16px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        height: 100%;
        text-decoration: none;
        color: inherit;
    }
    
    .action-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 40px rgba(0,0,0,0.15);
        color: inherit;
        text-decoration: none;
    }
    
    .action-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        font-size: 2rem;
        color: white;
    }
    
    .action-title {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--text-dark);
    }
    
    .action-description {
        color: #666;
        font-size: 0.95rem;
        line-height: 1.5;
    }
    
    .recent-documents {
        margin-bottom: 3rem;
    }
    
    .section-title {
        font-size: 1.8rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        color: var(--text-dark);
        display: flex;
        align-items: center;
    }
    
    .section-title i {
        margin-right: 0.5rem;
        color: var(--secondary-color);
    }
    
    .document-card {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        border: 1px solid var(--border-color);
        cursor: pointer;
    }
    
    .document-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        border-color: var(--secondary-color);
    }
    
    .document-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--text-dark);
    }
    
    .document-meta {
        display: flex;
        justify-content: between;
        align-items: center;
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 0.5rem;
    }
    
    .document-preview {
        color: #888;
        font-size: 0.9rem;
        line-height: 1.4;
        max-height: 3.6rem;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
    }
    
    .document-actions {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .document-card:hover .document-actions {
        opacity: 1;
    }
    
    .btn-sm {
        padding: 0.4rem 0.8rem;
        font-size: 0.85rem;
        border-radius: 6px;
    }
    
    .empty-state {
        text-align: center;
        padding: 3rem 1rem;
        color: #666;
    }
    
    .empty-state i {
        font-size: 4rem;
        color: var(--border-color);
        margin-bottom: 1rem;
    }
    
    .empty-state h3 {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
        color: var(--text-dark);
    }
    
    .templates-preview {
        margin-bottom: 3rem;
    }
    
    .template-card {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
        border: 1px solid var(--border-color);
        cursor: pointer;
        height: 100%;
    }
    
    .template-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        border-color: var(--secondary-color);
    }
    
    .template-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, var(--secondary-color) 0%, var(--accent-color) 100%);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 1.5rem;
        color: white;
    }
    
    .template-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--text-dark);
    }
    
    .template-description {
        color: #666;
        font-size: 0.9rem;
        line-height: 1.4;
    }
    
    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }
    
    @media (max-width: 768px) {
        .hero-section {
            padding: 2rem 0;
        }
        
        .welcome-title {
            font-size: 2rem;
        }
        
        .welcome-subtitle {
            font-size: 1.1rem;
        }
        
        .stat-number {
            font-size: 2rem;
        }
        
        .action-card {
            margin-bottom: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="container-fluid">
        <div class="hero-content text-center">
            <h1 class="welcome-title">Welcome back, {{ current_user.first_name }}!</h1>
            <p class="welcome-subtitle">Ready to create something amazing today?</p>
            
            <div class="row stats-row">
                <div class="col-md-4">
                    <div class="stat-item">
                        <span class="stat-number">{{ total_docs }}</span>
                        <span class="stat-label">Documents</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-item">
                        <span class="stat-number">{{ "{:,}".format(total_words) }}</span>
                        <span class="stat-label">Words Written</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-item">
                        <span class="stat-number">{{ current_user.created_at.strftime('%b %Y') }}</span>
                        <span class="stat-label">Member Since</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container-fluid">
    <!-- Quick Actions -->
    <section class="quick-actions">
        <h2 class="section-title">
            <i class="fas fa-bolt"></i>
            Quick Actions
        </h2>
        
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <a href="{{ url_for('editor') }}" class="action-card">
                    <div class="action-icon">
                        <i class="fas fa-plus"></i>
                    </div>
                    <h3 class="action-title">New Document</h3>
                    <p class="action-description">Start writing with a blank document</p>
                </a>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <a href="{{ url_for('templates') }}" class="action-card">
                    <div class="action-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h3 class="action-title">Use Template</h3>
                    <p class="action-description">Choose from professional templates</p>
                </a>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <a href="#" class="action-card" onclick="importDocument()">
                    <div class="action-icon">
                        <i class="fas fa-upload"></i>
                    </div>
                    <h3 class="action-title">Import Document</h3>
                    <p class="action-description">Upload existing documents</p>
                </a>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <a href="#" class="action-card" onclick="showCollaborations()">
                    <div class="action-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="action-title">Collaborations</h3>
                    <p class="action-description">View shared documents</p>
                </a>
            </div>
        </div>
    </section>
    
    <!-- Recent Documents -->
    <section class="recent-documents">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h2 class="section-title mb-0">
                <i class="fas fa-clock"></i>
                Recent Documents
            </h2>
            <a href="#" class="btn btn-outline-primary" onclick="showAllDocuments()">
                View All
            </a>
        </div>
        
        {% if recent_docs %}
            <div class="row">
                {% for doc in recent_docs %}
                <div class="col-lg-6 mb-3">
                    <div class="document-card" onclick="openDocument({{ doc.id }})">
                        <div class="document-title">{{ doc.title }}</div>
                        <div class="document-meta">
                            <span><i class="fas fa-calendar me-1"></i>{{ doc.updated_at.strftime('%b %d, %Y') }}</span>
                            <span class="ms-auto">
                                <i class="fas fa-font me-1"></i>{{ doc.word_count }} words
                            </span>
                        </div>
                        <div class="document-preview">
                            {{ doc.content[:150] }}{% if doc.content|length > 150 %}...{% endif %}
                        </div>
                        <div class="document-actions">
                            <button class="btn btn-sm btn-primary" onclick="event.stopPropagation(); openDocument({{ doc.id }})">
                                <i class="fas fa-edit me-1"></i>Edit
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" onclick="event.stopPropagation(); shareDocument({{ doc.id }})">
                                <i class="fas fa-share me-1"></i>Share
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="event.stopPropagation(); deleteDocument({{ doc.id }})">
                                <i class="fas fa-trash me-1"></i>Delete
                            </button>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="empty-state">
                <i class="fas fa-file-alt"></i>
                <h3>No documents yet</h3>
                <p>Create your first document to get started!</p>
                <a href="{{ url_for('editor') }}" class="btn btn-primary mt-2">
                    <i class="fas fa-plus me-2"></i>Create Document
                </a>
            </div>
        {% endif %}
    </section>
    
    <!-- Template Preview -->
    <section class="templates-preview">
        <h2 class="section-title">
            <i class="fas fa-layer-group"></i>
            Popular Templates
        </h2>
        
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="template-card" onclick="useTemplate('business-letter')">
                    <div class="template-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <h3 class="template-title">Business Letter</h3>
                    <p class="template-description">Professional letter template</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="template-card" onclick="useTemplate('resume')">
                    <div class="template-icon">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <h3 class="template-title">Resume</h3>
                    <p class="template-description">Modern resume layout</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="template-card" onclick="useTemplate('meeting-notes')">
                    <div class="template-icon">
                        <i class="fas fa-clipboard-list"></i>
                    </div>
                    <h3 class="template-title">Meeting Notes</h3>
                    <p class="template-description">Structured meeting template</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="template-card" onclick="useTemplate('project-proposal')">
                    <div class="template-icon">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <h3 class="template-title">Project Proposal</h3>
                    <p class="template-description">Professional proposal format</p>
                </div>
            </div>
        </div>
        
        <div class="text-center">
            <a href="{{ url_for('templates') }}" class="btn btn-outline-primary">
                <i class="fas fa-th-large me-2"></i>View All Templates
            </a>
        </div>
    </section>
</div>

<!-- Hidden file input for document import -->
<input type="file" id="fileImport" accept=".txt,.docx,.html" style="display: none;" onchange="handleFileImport(this)">
{% endblock %}

{% block scripts %}
<script>
    // Open document in editor
    function openDocument(docId) {
        window.location.href = `/editor/${docId}`;
    }
    
    // Use template
    function useTemplate(templateName) {
        window.location.href = `/templates?use=${templateName}`;
    }
    
    // Import document
    function importDocument() {
        document.getElementById('fileImport').click();
    }
    
    // Handle file import
    function handleFileImport(input) {
        const file = input.files[0];
        if (!file) return;
        
        const formData = new FormData();
        formData.append('file', file);
        
        // Show loading
        TritonQuill.showAlert('Importing document...', 'info');
        
        fetch('/api/documents/import', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                TritonQuill.showAlert('Document imported successfully!', 'success');
                setTimeout(() => {
                    window.location.href = `/editor/${data.document.id}`;
                }, 1000);
            } else {
                TritonQuill.showAlert(data.message, 'danger');
            }
        })
        .catch(error => {
            TritonQuill.showAlert('Failed to import document', 'danger');
        });
        
        // Reset input
        input.value = '';
    }
    
    // Share document
    function shareDocument(docId) {
        // This would open a share modal
        TritonQuill.showAlert('Share functionality coming soon!', 'info');
    }
    
    // Delete document
    function deleteDocument(docId) {
        if (!confirm('Are you sure you want to delete this document?')) {
            return;
        }
        
        fetch(`/api/documents/${docId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                TritonQuill.showAlert('Document deleted successfully', 'success');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                TritonQuill.showAlert(data.message, 'danger');
            }
        })
        .catch(error => {
            TritonQuill.showAlert('Failed to delete document', 'danger');
        });
    }
    
    // Show all documents
    function showAllDocuments() {
        // This would open a documents browser modal or page
        TritonQuill.showAlert('Documents browser coming soon!', 'info');
    }
    
    // Show collaborations
    function showCollaborations() {
        // This would open a collaborations modal or page
        TritonQuill.showAlert('Collaborations view coming soon!', 'info');
    }
    
    // Add some interactive animations
    document.addEventListener('DOMContentLoaded', function() {
        // Animate cards on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animation = 'fadeIn 0.6s ease-out forwards';
                }
            });
        }, observerOptions);
        
        // Observe all cards
        document.querySelectorAll('.action-card, .document-card, .template-card').forEach(card => {
            observer.observe(card);
        });
    });
</script>
{% endblock %}
