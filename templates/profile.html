{% extends "base.html" %}

{% block title %}Profile - <PERSON><PERSON>{% endblock %}

{% block styles %}
<style>
    .profile-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 3rem 0;
        margin-bottom: 2rem;
    }
    
    .profile-avatar {
        width: 120px;
        height: 120px;
        background: rgba(255,255,255,0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 3rem;
        margin: 0 auto 1.5rem;
        border: 4px solid rgba(255,255,255,0.3);
    }
    
    .profile-name {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .profile-email {
        font-size: 1.1rem;
        opacity: 0.9;
    }
    
    .profile-stats {
        display: flex;
        justify-content: center;
        gap: 3rem;
        margin-top: 2rem;
    }
    
    .stat-item {
        text-align: center;
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        display: block;
    }
    
    .stat-label {
        font-size: 0.9rem;
        opacity: 0.8;
    }
    
    .profile-content {
        max-width: 800px;
        margin: 0 auto;
    }
    
    .profile-section {
        background: white;
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }
    
    .section-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        color: var(--text-dark);
        display: flex;
        align-items: center;
    }
    
    .section-title i {
        margin-right: 0.5rem;
        color: var(--secondary-color);
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: var(--text-dark);
    }
    
    .form-control {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid var(--border-color);
        border-radius: 8px;
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus {
        outline: none;
        border-color: var(--secondary-color);
        box-shadow: 0 0 0 3px rgba(58, 147, 204, 0.1);
    }
    
    .btn-save {
        background: var(--primary-color);
        color: white;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .btn-save:hover {
        background: var(--accent-color);
        transform: translateY(-1px);
    }
    
    .btn-save:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none;
    }
    
    .activity-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        border-bottom: 1px solid var(--border-color);
        transition: background 0.2s ease;
    }
    
    .activity-item:hover {
        background: var(--background-light);
    }
    
    .activity-item:last-child {
        border-bottom: none;
    }
    
    .activity-icon {
        width: 40px;
        height: 40px;
        background: var(--secondary-color);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        margin-right: 1rem;
        font-size: 0.9rem;
    }
    
    .activity-content {
        flex: 1;
    }
    
    .activity-title {
        font-weight: 500;
        margin-bottom: 0.25rem;
        color: var(--text-dark);
    }
    
    .activity-time {
        font-size: 0.85rem;
        color: #666;
    }
    
    .preferences-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }
    
    .preference-item {
        display: flex;
        justify-content: between;
        align-items: center;
        padding: 1rem;
        background: var(--background-light);
        border-radius: 8px;
    }
    
    .preference-label {
        font-weight: 500;
        color: var(--text-dark);
    }
    
    .preference-description {
        font-size: 0.85rem;
        color: #666;
        margin-top: 0.25rem;
    }
    
    .toggle-switch {
        position: relative;
        width: 50px;
        height: 24px;
        background: #ccc;
        border-radius: 12px;
        cursor: pointer;
        transition: background 0.3s ease;
    }
    
    .toggle-switch.active {
        background: var(--secondary-color);
    }
    
    .toggle-switch::after {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        width: 20px;
        height: 20px;
        background: white;
        border-radius: 50%;
        transition: transform 0.3s ease;
    }
    
    .toggle-switch.active::after {
        transform: translateX(26px);
    }
    
    @media (max-width: 768px) {
        .profile-stats {
            gap: 2rem;
        }
        
        .stat-number {
            font-size: 1.5rem;
        }
        
        .profile-section {
            padding: 1.5rem;
        }
        
        .preferences-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Profile Header -->
<section class="profile-header">
    <div class="container-fluid text-center">
        <div class="profile-avatar">
            <i class="fas fa-user"></i>
        </div>
        <h1 class="profile-name">{{ user.get_full_name() }}</h1>
        <p class="profile-email">{{ user.email }}</p>
        
        <div class="profile-stats">
            <div class="stat-item">
                <span class="stat-number">{{ user.documents|length }}</span>
                <span class="stat-label">Documents</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">{{ user.created_at.strftime('%b %Y') }}</span>
                <span class="stat-label">Member Since</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">{{ user.last_login.strftime('%b %d') if user.last_login else 'Never' }}</span>
                <span class="stat-label">Last Login</span>
            </div>
        </div>
    </div>
</section>

<div class="container-fluid">
    <div class="profile-content">
        <!-- Personal Information -->
        <div class="profile-section">
            <h2 class="section-title">
                <i class="fas fa-user-edit"></i>
                Personal Information
            </h2>
            
            <form id="profileForm">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">First Name</label>
                            <input type="text" class="form-control" name="first_name" 
                                   value="{{ user.first_name }}" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">Last Name</label>
                            <input type="text" class="form-control" name="last_name" 
                                   value="{{ user.last_name }}" required>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Email Address</label>
                    <input type="email" class="form-control" value="{{ user.email }}" disabled>
                    <small class="text-muted">Email cannot be changed. Contact support if needed.</small>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Username</label>
                    <input type="text" class="form-control" value="{{ user.username }}" disabled>
                    <small class="text-muted">Username cannot be changed.</small>
                </div>
                
                <button type="submit" class="btn-save">
                    <i class="fas fa-save me-2"></i>Save Changes
                </button>
            </form>
        </div>
        
        <!-- Change Password -->
        <div class="profile-section">
            <h2 class="section-title">
                <i class="fas fa-lock"></i>
                Change Password
            </h2>
            
            <form id="passwordForm">
                <div class="form-group">
                    <label class="form-label">Current Password</label>
                    <input type="password" class="form-control" name="current_password" required>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">New Password</label>
                            <input type="password" class="form-control" name="new_password" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">Confirm New Password</label>
                            <input type="password" class="form-control" name="confirm_password" required>
                        </div>
                    </div>
                </div>
                
                <button type="submit" class="btn-save">
                    <i class="fas fa-key me-2"></i>Change Password
                </button>
            </form>
        </div>
        
        <!-- Preferences -->
        <div class="profile-section">
            <h2 class="section-title">
                <i class="fas fa-cog"></i>
                Preferences
            </h2>
            
            <div class="preferences-grid">
                <div class="preference-item">
                    <div>
                        <div class="preference-label">Email Notifications</div>
                        <div class="preference-description">Receive updates about your documents</div>
                    </div>
                    <div class="toggle-switch active" onclick="togglePreference(this)"></div>
                </div>
                
                <div class="preference-item">
                    <div>
                        <div class="preference-label">Auto-save</div>
                        <div class="preference-description">Automatically save documents while editing</div>
                    </div>
                    <div class="toggle-switch active" onclick="togglePreference(this)"></div>
                </div>
                
                <div class="preference-item">
                    <div>
                        <div class="preference-label">Collaboration Notifications</div>
                        <div class="preference-description">Get notified when others edit shared documents</div>
                    </div>
                    <div class="toggle-switch" onclick="togglePreference(this)"></div>
                </div>
                
                <div class="preference-item">
                    <div>
                        <div class="preference-label">Dark Mode</div>
                        <div class="preference-description">Use dark theme for the editor</div>
                    </div>
                    <div class="toggle-switch" onclick="togglePreference(this)"></div>
                </div>
            </div>
        </div>
        
        <!-- Recent Activity -->
        <div class="profile-section">
            <h2 class="section-title">
                <i class="fas fa-history"></i>
                Recent Activity
            </h2>
            
            <div class="activity-list">
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">Created "Project Proposal Draft"</div>
                        <div class="activity-time">2 hours ago</div>
                    </div>
                </div>
                
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">Updated "Meeting Notes - Q3 Planning"</div>
                        <div class="activity-time">5 hours ago</div>
                    </div>
                </div>
                
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-share"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">Shared "Business Letter Template" with team</div>
                        <div class="activity-time">1 day ago</div>
                    </div>
                </div>
                
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">Exported "Annual Report" as PDF</div>
                        <div class="activity-time">2 days ago</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Account Actions -->
        <div class="profile-section">
            <h2 class="section-title">
                <i class="fas fa-user-cog"></i>
                Account Actions
            </h2>
            
            <div class="d-flex gap-3 flex-wrap">
                <button class="btn btn-outline-primary" onclick="exportData()">
                    <i class="fas fa-download me-2"></i>Export My Data
                </button>
                
                <button class="btn btn-outline-secondary" onclick="deactivateAccount()">
                    <i class="fas fa-user-slash me-2"></i>Deactivate Account
                </button>
                
                <button class="btn btn-outline-danger" onclick="deleteAccount()">
                    <i class="fas fa-trash me-2"></i>Delete Account
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Handle profile form submission
    document.getElementById('profileForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const button = this.querySelector('button[type="submit"]');
        const originalText = button.innerHTML;
        
        button.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Saving...';
        button.disabled = true;
        
        const formData = new FormData(this);
        const data = {
            first_name: formData.get('first_name'),
            last_name: formData.get('last_name')
        };
        
        fetch('{{ url_for("auth.update_profile") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                TritonQuill.showAlert('Profile updated successfully!', 'success');
                // Update the header name
                setTimeout(() => location.reload(), 1000);
            } else {
                TritonQuill.showAlert(data.message, 'danger');
            }
        })
        .catch(error => {
            TritonQuill.showAlert('Failed to update profile', 'danger');
        })
        .finally(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        });
    });
    
    // Handle password form submission
    document.getElementById('passwordForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const button = this.querySelector('button[type="submit"]');
        const originalText = button.innerHTML;
        
        button.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Changing...';
        button.disabled = true;
        
        const formData = new FormData(this);
        const data = {
            current_password: formData.get('current_password'),
            new_password: formData.get('new_password'),
            confirm_password: formData.get('confirm_password')
        };
        
        fetch('{{ url_for("auth.change_password") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                TritonQuill.showAlert('Password changed successfully!', 'success');
                this.reset();
            } else {
                TritonQuill.showAlert(data.message, 'danger');
            }
        })
        .catch(error => {
            TritonQuill.showAlert('Failed to change password', 'danger');
        })
        .finally(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        });
    });
    
    // Toggle preference
    function togglePreference(element) {
        element.classList.toggle('active');
        // Here you would save the preference to the server
        TritonQuill.showAlert('Preference updated', 'success');
    }
    
    // Account actions
    function exportData() {
        TritonQuill.showAlert('Data export functionality coming soon!', 'info');
    }
    
    function deactivateAccount() {
        if (confirm('Are you sure you want to deactivate your account? You can reactivate it later by logging in.')) {
            TritonQuill.showAlert('Account deactivation functionality coming soon!', 'info');
        }
    }
    
    function deleteAccount() {
        if (confirm('Are you sure you want to permanently delete your account? This action cannot be undone.')) {
            const confirmation = prompt('Type "DELETE" to confirm account deletion:');
            if (confirmation === 'DELETE') {
                TritonQuill.showAlert('Account deletion functionality coming soon!', 'info');
            }
        }
    }
</script>
{% endblock %}
