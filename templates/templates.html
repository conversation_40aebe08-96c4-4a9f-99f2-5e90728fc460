{% extends "base.html" %}

{% block title %}Templates - <PERSON><PERSON>{% endblock %}

{% block styles %}
<style>
    .templates-hero {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 3rem 0;
        margin-bottom: 2rem;
    }
    
    .hero-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }
    
    .hero-subtitle {
        font-size: 1.2rem;
        font-weight: 300;
        opacity: 0.9;
    }
    
    .template-categories {
        margin-bottom: 2rem;
    }
    
    .category-filter {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
        justify-content: center;
        margin-bottom: 2rem;
    }
    
    .category-btn {
        padding: 0.75rem 1.5rem;
        border: 2px solid var(--border-color);
        background: white;
        color: var(--text-dark);
        border-radius: 25px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 500;
        text-decoration: none;
    }
    
    .category-btn:hover,
    .category-btn.active {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
        text-decoration: none;
    }
    
    .templates-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }
    
    .template-card {
        background: white;
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        cursor: pointer;
        border: 1px solid var(--border-color);
    }
    
    .template-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 40px rgba(0,0,0,0.15);
        border-color: var(--secondary-color);
    }
    
    .template-preview {
        height: 200px;
        background: var(--background-light);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
    }
    
    .template-preview::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, 
            rgba(7, 66, 119, 0.05) 0%, 
            rgba(58, 147, 204, 0.05) 100%);
    }
    
    .template-icon {
        font-size: 4rem;
        color: var(--secondary-color);
        z-index: 2;
        position: relative;
    }
    
    .template-content {
        padding: 1.5rem;
    }
    
    .template-title {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--text-dark);
    }
    
    .template-description {
        color: #666;
        font-size: 0.95rem;
        line-height: 1.5;
        margin-bottom: 1rem;
    }
    
    .template-meta {
        display: flex;
        justify-content: between;
        align-items: center;
        font-size: 0.85rem;
        color: #888;
        margin-bottom: 1rem;
    }
    
    .template-category {
        background: var(--background-light);
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
        font-weight: 500;
        color: var(--text-dark);
    }
    
    .template-actions {
        display: flex;
        gap: 0.5rem;
    }
    
    .btn-template {
        flex: 1;
        padding: 0.75rem;
        border-radius: 8px;
        font-weight: 500;
        text-align: center;
        text-decoration: none;
        transition: all 0.3s ease;
    }
    
    .btn-primary {
        background: var(--primary-color);
        color: white;
        border: none;
    }
    
    .btn-primary:hover {
        background: var(--accent-color);
        color: white;
        text-decoration: none;
    }
    
    .btn-outline {
        background: transparent;
        color: var(--primary-color);
        border: 1px solid var(--primary-color);
    }
    
    .btn-outline:hover {
        background: var(--primary-color);
        color: white;
        text-decoration: none;
    }
    
    .search-section {
        max-width: 600px;
        margin: 0 auto 3rem;
    }
    
    .search-input {
        width: 100%;
        padding: 1rem 1.5rem;
        border: 2px solid var(--border-color);
        border-radius: 50px;
        font-size: 1.1rem;
        transition: all 0.3s ease;
    }
    
    .search-input:focus {
        outline: none;
        border-color: var(--secondary-color);
        box-shadow: 0 0 0 3px rgba(58, 147, 204, 0.1);
    }
    
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: #666;
    }
    
    .empty-state i {
        font-size: 5rem;
        color: var(--border-color);
        margin-bottom: 2rem;
    }
    
    .empty-state h3 {
        font-size: 2rem;
        margin-bottom: 1rem;
        color: var(--text-dark);
    }
    
    .custom-template-section {
        background: var(--background-light);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 3rem;
        text-align: center;
    }
    
    .custom-template-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, var(--secondary-color) 0%, var(--accent-color) 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        font-size: 2rem;
        color: white;
    }
    
    @media (max-width: 768px) {
        .templates-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }
        
        .hero-title {
            font-size: 2rem;
        }
        
        .category-filter {
            justify-content: flex-start;
            overflow-x: auto;
            padding-bottom: 0.5rem;
        }
        
        .category-btn {
            white-space: nowrap;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="templates-hero">
    <div class="container-fluid text-center">
        <h1 class="hero-title">Document Templates</h1>
        <p class="hero-subtitle">Start with professionally designed templates to save time and create beautiful documents</p>
    </div>
</section>

<div class="container-fluid">
    <!-- Search Section -->
    <div class="search-section">
        <input type="text" class="search-input" id="templateSearch" 
               placeholder="Search templates..." onkeyup="filterTemplates()">
    </div>
    
    <!-- Category Filter -->
    <div class="category-filter">
        <a href="#" class="category-btn active" onclick="filterByCategory('all')">All Templates</a>
        <a href="#" class="category-btn" onclick="filterByCategory('business')">Business</a>
        <a href="#" class="category-btn" onclick="filterByCategory('personal')">Personal</a>
        <a href="#" class="category-btn" onclick="filterByCategory('academic')">Academic</a>
        <a href="#" class="category-btn" onclick="filterByCategory('creative')">Creative</a>
    </div>
    
    <!-- Custom Template Section -->
    <div class="custom-template-section">
        <div class="custom-template-icon">
            <i class="fas fa-plus"></i>
        </div>
        <h3>Create Your Own Template</h3>
        <p class="mb-3">Design a custom template that fits your specific needs</p>
        <a href="{{ url_for('editor') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Start from Scratch
        </a>
    </div>
    
    <!-- Templates Grid -->
    <div class="templates-grid" id="templatesGrid">
        <!-- Blank Document -->
        <div class="template-card" data-category="basic">
            <div class="template-preview">
                <i class="template-icon fas fa-file-alt"></i>
            </div>
            <div class="template-content">
                <h3 class="template-title">Blank Document</h3>
                <p class="template-description">Start with a clean slate. Perfect for any type of document.</p>
                <div class="template-meta">
                    <span class="template-category">Basic</span>
                    <span>Most Popular</span>
                </div>
                <div class="template-actions">
                    <a href="{{ url_for('editor') }}" class="btn-template btn-primary">
                        <i class="fas fa-plus me-2"></i>Use Template
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Business Letter -->
        <div class="template-card" data-category="business">
            <div class="template-preview">
                <i class="template-icon fas fa-envelope"></i>
            </div>
            <div class="template-content">
                <h3 class="template-title">Business Letter</h3>
                <p class="template-description">Professional letter template with proper formatting and structure.</p>
                <div class="template-meta">
                    <span class="template-category">Business</span>
                    <span>Formal</span>
                </div>
                <div class="template-actions">
                    <a href="#" onclick="useTemplate('business-letter')" class="btn-template btn-primary">
                        <i class="fas fa-plus me-2"></i>Use Template
                    </a>
                    <a href="#" onclick="previewTemplate('business-letter')" class="btn-template btn-outline">
                        <i class="fas fa-eye me-2"></i>Preview
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Resume -->
        <div class="template-card" data-category="personal">
            <div class="template-preview">
                <i class="template-icon fas fa-user-tie"></i>
            </div>
            <div class="template-content">
                <h3 class="template-title">Professional Resume</h3>
                <p class="template-description">Modern resume layout that highlights your skills and experience.</p>
                <div class="template-meta">
                    <span class="template-category">Personal</span>
                    <span>Career</span>
                </div>
                <div class="template-actions">
                    <a href="#" onclick="useTemplate('resume')" class="btn-template btn-primary">
                        <i class="fas fa-plus me-2"></i>Use Template
                    </a>
                    <a href="#" onclick="previewTemplate('resume')" class="btn-template btn-outline">
                        <i class="fas fa-eye me-2"></i>Preview
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Meeting Notes -->
        <div class="template-card" data-category="business">
            <div class="template-preview">
                <i class="template-icon fas fa-clipboard-list"></i>
            </div>
            <div class="template-content">
                <h3 class="template-title">Meeting Notes</h3>
                <p class="template-description">Structured template for capturing meeting discussions and action items.</p>
                <div class="template-meta">
                    <span class="template-category">Business</span>
                    <span>Productivity</span>
                </div>
                <div class="template-actions">
                    <a href="#" onclick="useTemplate('meeting-notes')" class="btn-template btn-primary">
                        <i class="fas fa-plus me-2"></i>Use Template
                    </a>
                    <a href="#" onclick="previewTemplate('meeting-notes')" class="btn-template btn-outline">
                        <i class="fas fa-eye me-2"></i>Preview
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Project Proposal -->
        <div class="template-card" data-category="business">
            <div class="template-preview">
                <i class="template-icon fas fa-project-diagram"></i>
            </div>
            <div class="template-content">
                <h3 class="template-title">Project Proposal</h3>
                <p class="template-description">Comprehensive template for presenting project ideas and plans.</p>
                <div class="template-meta">
                    <span class="template-category">Business</span>
                    <span>Planning</span>
                </div>
                <div class="template-actions">
                    <a href="#" onclick="useTemplate('project-proposal')" class="btn-template btn-primary">
                        <i class="fas fa-plus me-2"></i>Use Template
                    </a>
                    <a href="#" onclick="previewTemplate('project-proposal')" class="btn-template btn-outline">
                        <i class="fas fa-eye me-2"></i>Preview
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Essay Template -->
        <div class="template-card" data-category="academic">
            <div class="template-preview">
                <i class="template-icon fas fa-graduation-cap"></i>
            </div>
            <div class="template-content">
                <h3 class="template-title">Academic Essay</h3>
                <p class="template-description">Structured essay template with proper academic formatting.</p>
                <div class="template-meta">
                    <span class="template-category">Academic</span>
                    <span>Education</span>
                </div>
                <div class="template-actions">
                    <a href="#" onclick="useTemplate('academic-essay')" class="btn-template btn-primary">
                        <i class="fas fa-plus me-2"></i>Use Template
                    </a>
                    <a href="#" onclick="previewTemplate('academic-essay')" class="btn-template btn-outline">
                        <i class="fas fa-eye me-2"></i>Preview
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Newsletter -->
        <div class="template-card" data-category="creative">
            <div class="template-preview">
                <i class="template-icon fas fa-newspaper"></i>
            </div>
            <div class="template-content">
                <h3 class="template-title">Newsletter</h3>
                <p class="template-description">Eye-catching newsletter template for sharing updates and news.</p>
                <div class="template-meta">
                    <span class="template-category">Creative</span>
                    <span>Marketing</span>
                </div>
                <div class="template-actions">
                    <a href="#" onclick="useTemplate('newsletter')" class="btn-template btn-primary">
                        <i class="fas fa-plus me-2"></i>Use Template
                    </a>
                    <a href="#" onclick="previewTemplate('newsletter')" class="btn-template btn-outline">
                        <i class="fas fa-eye me-2"></i>Preview
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Invoice -->
        <div class="template-card" data-category="business">
            <div class="template-preview">
                <i class="template-icon fas fa-file-invoice"></i>
            </div>
            <div class="template-content">
                <h3 class="template-title">Invoice</h3>
                <p class="template-description">Professional invoice template for billing clients and customers.</p>
                <div class="template-meta">
                    <span class="template-category">Business</span>
                    <span>Finance</span>
                </div>
                <div class="template-actions">
                    <a href="#" onclick="useTemplate('invoice')" class="btn-template btn-primary">
                        <i class="fas fa-plus me-2"></i>Use Template
                    </a>
                    <a href="#" onclick="previewTemplate('invoice')" class="btn-template btn-outline">
                        <i class="fas fa-eye me-2"></i>Preview
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Empty State (hidden by default) -->
    <div class="empty-state" id="emptyState" style="display: none;">
        <i class="fas fa-search"></i>
        <h3>No templates found</h3>
        <p>Try adjusting your search or browse different categories</p>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Filter templates by category
    function filterByCategory(category) {
        // Update active button
        document.querySelectorAll('.category-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        event.target.classList.add('active');
        
        // Filter templates
        const templates = document.querySelectorAll('.template-card');
        let visibleCount = 0;
        
        templates.forEach(template => {
            if (category === 'all' || template.dataset.category === category) {
                template.style.display = 'block';
                visibleCount++;
            } else {
                template.style.display = 'none';
            }
        });
        
        // Show/hide empty state
        document.getElementById('emptyState').style.display = visibleCount === 0 ? 'block' : 'none';
    }
    
    // Filter templates by search
    function filterTemplates() {
        const searchTerm = document.getElementById('templateSearch').value.toLowerCase();
        const templates = document.querySelectorAll('.template-card');
        let visibleCount = 0;
        
        templates.forEach(template => {
            const title = template.querySelector('.template-title').textContent.toLowerCase();
            const description = template.querySelector('.template-description').textContent.toLowerCase();
            
            if (title.includes(searchTerm) || description.includes(searchTerm)) {
                template.style.display = 'block';
                visibleCount++;
            } else {
                template.style.display = 'none';
            }
        });
        
        // Show/hide empty state
        document.getElementById('emptyState').style.display = visibleCount === 0 ? 'block' : 'none';
    }
    
    // Use template
    function useTemplate(templateName) {
        // Create new document from template
        fetch('/api/templates/use', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ template: templateName })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = `/editor/${data.document.id}`;
            } else {
                TritonQuill.showAlert('Failed to create document from template', 'error');
            }
        })
        .catch(error => {
            TritonQuill.showAlert('Failed to create document from template', 'error');
        });
    }
    
    // Preview template
    function previewTemplate(templateName) {
        // This would open a modal with template preview
        TritonQuill.showAlert('Template preview coming soon!', 'info');
    }
    
    // Add animation on scroll
    document.addEventListener('DOMContentLoaded', function() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animation = 'fadeIn 0.6s ease-out forwards';
                }
            });
        }, observerOptions);
        
        document.querySelectorAll('.template-card').forEach(card => {
            observer.observe(card);
        });
    });
</script>
{% endblock %}
