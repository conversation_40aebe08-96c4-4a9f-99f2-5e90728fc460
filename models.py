from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
import json

db = SQLAlchemy()

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    is_active = db.Column(db.<PERSON>, default=True)
    profile_picture = db.Column(db.String(255))
    
    # Relationships
    documents = db.relationship('Document', backref='owner', lazy=True, cascade='all, delete-orphan')
    collaborations = db.relationship('Collaboration', backref='user', lazy=True)
    comments = db.relationship('Comment', backref='author', lazy=True)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def get_full_name(self):
        return f"{self.first_name} {self.last_name}"
    
    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'full_name': self.get_full_name(),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }

class Document(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    content = db.Column(db.Text)
    content_json = db.Column(db.Text)  # Rich text content as JSON
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    owner_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    is_public = db.Column(db.Boolean, default=False)
    is_template = db.Column(db.Boolean, default=False)
    template_category = db.Column(db.String(50))
    word_count = db.Column(db.Integer, default=0)
    character_count = db.Column(db.Integer, default=0)
    
    # Document settings
    font_family = db.Column(db.String(50), default='Arial')
    font_size = db.Column(db.Integer, default=12)
    line_spacing = db.Column(db.Float, default=1.0)
    margin_top = db.Column(db.Float, default=1.0)
    margin_bottom = db.Column(db.Float, default=1.0)
    margin_left = db.Column(db.Float, default=1.0)
    margin_right = db.Column(db.Float, default=1.0)
    
    # Relationships
    versions = db.relationship('DocumentVersion', backref='document', lazy=True, cascade='all, delete-orphan')
    collaborations = db.relationship('Collaboration', backref='document', lazy=True, cascade='all, delete-orphan')
    comments = db.relationship('Comment', backref='document', lazy=True, cascade='all, delete-orphan')
    
    def get_content_json(self):
        if self.content_json:
            return json.loads(self.content_json)
        return {"ops": [{"insert": self.content or ""}]}
    
    def set_content_json(self, content_data):
        self.content_json = json.dumps(content_data)
        # Extract plain text for search and word count
        if isinstance(content_data, dict) and 'ops' in content_data:
            plain_text = ""
            for op in content_data['ops']:
                if isinstance(op, dict) and 'insert' in op:
                    plain_text += str(op['insert'])
            self.content = plain_text
            self.word_count = len(plain_text.split())
            self.character_count = len(plain_text)
    
    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'content_json': self.get_content_json(),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'owner_id': self.owner_id,
            'owner_name': self.owner.get_full_name() if self.owner else None,
            'is_public': self.is_public,
            'is_template': self.is_template,
            'template_category': self.template_category,
            'word_count': self.word_count,
            'character_count': self.character_count,
            'font_family': self.font_family,
            'font_size': self.font_size,
            'line_spacing': self.line_spacing
        }

class DocumentVersion(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    document_id = db.Column(db.Integer, db.ForeignKey('document.id'), nullable=False)
    version_number = db.Column(db.Integer, nullable=False)
    content = db.Column(db.Text)
    content_json = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    change_summary = db.Column(db.String(500))
    
    creator = db.relationship('User', backref='document_versions')
    
    def get_content_json(self):
        if self.content_json:
            return json.loads(self.content_json)
        return {"ops": [{"insert": self.content or ""}]}

class Collaboration(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    document_id = db.Column(db.Integer, db.ForeignKey('document.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    permission = db.Column(db.String(20), nullable=False)  # 'view', 'comment', 'edit'
    invited_at = db.Column(db.DateTime, default=datetime.utcnow)
    invited_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    accepted_at = db.Column(db.DateTime)
    
    inviter = db.relationship('User', foreign_keys=[invited_by], backref='sent_invitations')

class Comment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    document_id = db.Column(db.Integer, db.ForeignKey('document.id'), nullable=False)
    author_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    content = db.Column(db.Text, nullable=False)
    position_start = db.Column(db.Integer)  # Character position in document
    position_end = db.Column(db.Integer)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_resolved = db.Column(db.Boolean, default=False)
    parent_id = db.Column(db.Integer, db.ForeignKey('comment.id'))  # For replies
    
    replies = db.relationship('Comment', backref=db.backref('parent', remote_side=[id]))
    
    def to_dict(self):
        return {
            'id': self.id,
            'content': self.content,
            'author_name': self.author.get_full_name(),
            'author_id': self.author_id,
            'position_start': self.position_start,
            'position_end': self.position_end,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'is_resolved': self.is_resolved,
            'parent_id': self.parent_id
        }
