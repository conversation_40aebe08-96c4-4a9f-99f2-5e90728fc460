from flask import Blueprint, request, jsonify, send_file
from flask_login import login_required, current_user
from models import Document, DocumentVersion, Collaboration, Comment, User, db
from datetime import datetime
import json
import io
from docx import Document as DocxDocument
from docx.shared import Inches
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
import os

api_bp = Blueprint('api', __name__, url_prefix='/api')

@api_bp.route('/documents', methods=['GET'])
@login_required
def get_documents():
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    search = request.args.get('search', '')
    
    query = Document.query.filter(
        (Document.owner_id == current_user.id) |
        (Document.collaborations.any(Collaboration.user_id == current_user.id))
    )
    
    if search:
        query = query.filter(
            (Document.title.contains(search)) |
            (Document.content.contains(search))
        )
    
    documents = query.order_by(Document.updated_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'documents': [doc.to_dict() for doc in documents.items],
        'total': documents.total,
        'pages': documents.pages,
        'current_page': page,
        'has_next': documents.has_next,
        'has_prev': documents.has_prev
    })

@api_bp.route('/documents', methods=['POST'])
@login_required
def create_document():
    data = request.get_json()
    title = data.get('title', 'Untitled Document').strip()
    content = data.get('content', '')
    content_json = data.get('content_json', {"ops": [{"insert": ""}]})
    
    if not title:
        return jsonify({'success': False, 'message': 'Title is required'}), 400
    
    try:
        document = Document(
            title=title,
            content=content,
            owner_id=current_user.id
        )
        document.set_content_json(content_json)
        
        db.session.add(document)
        db.session.commit()
        
        # Create initial version
        version = DocumentVersion(
            document_id=document.id,
            version_number=1,
            content=document.content,
            content_json=document.content_json,
            created_by=current_user.id,
            change_summary="Initial version"
        )
        db.session.add(version)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Document created successfully',
            'document': document.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'Failed to create document'}), 500

@api_bp.route('/documents/<int:doc_id>', methods=['GET'])
@login_required
def get_document(doc_id):
    document = Document.query.get_or_404(doc_id)
    
    # Check permissions
    if (document.owner_id != current_user.id and 
        not document.collaborations.filter_by(user_id=current_user.id).first() and
        not document.is_public):
        return jsonify({'success': False, 'message': 'Access denied'}), 403
    
    return jsonify({
        'success': True,
        'document': document.to_dict()
    })

@api_bp.route('/documents/<int:doc_id>', methods=['PUT'])
@login_required
def update_document(doc_id):
    document = Document.query.get_or_404(doc_id)
    
    # Check permissions
    collaboration = document.collaborations.filter_by(user_id=current_user.id).first()
    if (document.owner_id != current_user.id and 
        (not collaboration or collaboration.permission not in ['edit'])):
        return jsonify({'success': False, 'message': 'Edit permission required'}), 403
    
    data = request.get_json()
    
    try:
        # Update document
        if 'title' in data:
            document.title = data['title'].strip()
        
        if 'content_json' in data:
            old_content = document.content_json
            document.set_content_json(data['content_json'])
            
            # Create new version if content changed significantly
            if old_content != document.content_json:
                version_count = DocumentVersion.query.filter_by(document_id=doc_id).count()
                version = DocumentVersion(
                    document_id=doc_id,
                    version_number=version_count + 1,
                    content=document.content,
                    content_json=document.content_json,
                    created_by=current_user.id,
                    change_summary=data.get('change_summary', 'Document updated')
                )
                db.session.add(version)
        
        # Update document settings
        for field in ['font_family', 'font_size', 'line_spacing', 
                     'margin_top', 'margin_bottom', 'margin_left', 'margin_right']:
            if field in data:
                setattr(document, field, data[field])
        
        document.updated_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Document updated successfully',
            'document': document.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'Failed to update document'}), 500

@api_bp.route('/documents/<int:doc_id>', methods=['DELETE'])
@login_required
def delete_document(doc_id):
    document = Document.query.get_or_404(doc_id)
    
    # Only owner can delete
    if document.owner_id != current_user.id:
        return jsonify({'success': False, 'message': 'Only owner can delete document'}), 403
    
    try:
        db.session.delete(document)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Document deleted successfully'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'Failed to delete document'}), 500

@api_bp.route('/documents/<int:doc_id>/versions', methods=['GET'])
@login_required
def get_document_versions(doc_id):
    document = Document.query.get_or_404(doc_id)
    
    # Check permissions
    if (document.owner_id != current_user.id and 
        not document.collaborations.filter_by(user_id=current_user.id).first() and
        not document.is_public):
        return jsonify({'success': False, 'message': 'Access denied'}), 403
    
    versions = DocumentVersion.query.filter_by(document_id=doc_id)\
        .order_by(DocumentVersion.version_number.desc()).all()
    
    return jsonify({
        'versions': [{
            'id': v.id,
            'version_number': v.version_number,
            'created_at': v.created_at.isoformat(),
            'created_by': v.creator.get_full_name(),
            'change_summary': v.change_summary,
            'content_json': v.get_content_json()
        } for v in versions]
    })

@api_bp.route('/documents/<int:doc_id>/collaborate', methods=['POST'])
@login_required
def add_collaborator(doc_id):
    document = Document.query.get_or_404(doc_id)
    
    # Only owner can add collaborators
    if document.owner_id != current_user.id:
        return jsonify({'success': False, 'message': 'Only owner can add collaborators'}), 403
    
    data = request.get_json()
    email = data.get('email', '').strip().lower()
    permission = data.get('permission', 'view')
    
    if permission not in ['view', 'comment', 'edit']:
        return jsonify({'success': False, 'message': 'Invalid permission level'}), 400
    
    user = User.query.filter_by(email=email).first()
    if not user:
        return jsonify({'success': False, 'message': 'User not found'}), 404
    
    if user.id == current_user.id:
        return jsonify({'success': False, 'message': 'Cannot collaborate with yourself'}), 400
    
    # Check if already collaborating
    existing = Collaboration.query.filter_by(document_id=doc_id, user_id=user.id).first()
    if existing:
        existing.permission = permission
        existing.accepted_at = datetime.utcnow()
    else:
        collaboration = Collaboration(
            document_id=doc_id,
            user_id=user.id,
            permission=permission,
            invited_by=current_user.id,
            accepted_at=datetime.utcnow()
        )
        db.session.add(collaboration)
    
    try:
        db.session.commit()
        return jsonify({
            'success': True,
            'message': f'Collaborator added with {permission} permission'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'Failed to add collaborator'}), 500

@api_bp.route('/documents/<int:doc_id>/comments', methods=['GET'])
@login_required
def get_comments(doc_id):
    document = Document.query.get_or_404(doc_id)
    
    # Check permissions
    if (document.owner_id != current_user.id and 
        not document.collaborations.filter_by(user_id=current_user.id).first() and
        not document.is_public):
        return jsonify({'success': False, 'message': 'Access denied'}), 403
    
    comments = Comment.query.filter_by(document_id=doc_id, parent_id=None)\
        .order_by(Comment.created_at.desc()).all()
    
    return jsonify({
        'comments': [comment.to_dict() for comment in comments]
    })

@api_bp.route('/documents/<int:doc_id>/comments', methods=['POST'])
@login_required
def add_comment(doc_id):
    document = Document.query.get_or_404(doc_id)
    
    # Check permissions
    collaboration = document.collaborations.filter_by(user_id=current_user.id).first()
    if (document.owner_id != current_user.id and 
        (not collaboration or collaboration.permission not in ['comment', 'edit'])):
        return jsonify({'success': False, 'message': 'Comment permission required'}), 403
    
    data = request.get_json()
    content = data.get('content', '').strip()
    
    if not content:
        return jsonify({'success': False, 'message': 'Comment content is required'}), 400
    
    try:
        comment = Comment(
            document_id=doc_id,
            author_id=current_user.id,
            content=content,
            position_start=data.get('position_start'),
            position_end=data.get('position_end'),
            parent_id=data.get('parent_id')
        )
        
        db.session.add(comment)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Comment added successfully',
            'comment': comment.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'Failed to add comment'}), 500

@api_bp.route('/documents/<int:doc_id>/export/<format>')
@login_required
def export_document(doc_id, format):
    document = Document.query.get_or_404(doc_id)
    
    # Check permissions
    if (document.owner_id != current_user.id and 
        not document.collaborations.filter_by(user_id=current_user.id).first() and
        not document.is_public):
        return jsonify({'success': False, 'message': 'Access denied'}), 403
    
    if format == 'docx':
        return export_to_docx(document)
    elif format == 'pdf':
        return export_to_pdf(document)
    elif format == 'txt':
        return export_to_txt(document)
    else:
        return jsonify({'success': False, 'message': 'Unsupported format'}), 400

def export_to_docx(document):
    doc = DocxDocument()
    doc.add_heading(document.title, 0)
    doc.add_paragraph(document.content or '')
    
    # Save to memory
    file_stream = io.BytesIO()
    doc.save(file_stream)
    file_stream.seek(0)
    
    return send_file(
        file_stream,
        as_attachment=True,
        download_name=f"{document.title}.docx",
        mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    )

def export_to_pdf(document):
    buffer = io.BytesIO()
    p = canvas.Canvas(buffer, pagesize=letter)
    
    # Add title
    p.setFont("Helvetica-Bold", 16)
    p.drawString(72, 750, document.title)
    
    # Add content
    p.setFont("Helvetica", 12)
    text_lines = (document.content or '').split('\n')
    y_position = 720
    
    for line in text_lines:
        if y_position < 72:  # Start new page
            p.showPage()
            p.setFont("Helvetica", 12)
            y_position = 750
        
        p.drawString(72, y_position, line[:80])  # Limit line length
        y_position -= 15
    
    p.save()
    buffer.seek(0)
    
    return send_file(
        buffer,
        as_attachment=True,
        download_name=f"{document.title}.pdf",
        mimetype='application/pdf'
    )

def export_to_txt(document):
    content = f"{document.title}\n{'='*len(document.title)}\n\n{document.content or ''}"
    
    return send_file(
        io.BytesIO(content.encode('utf-8')),
        as_attachment=True,
        download_name=f"{document.title}.txt",
        mimetype='text/plain'
    )
