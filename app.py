from flask import Flask, render_template, redirect, url_for
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, login_required, current_user
from flask_socketio import Socket<PERSON>, emit, join_room, leave_room
from config import Config
from models import db, User, Document
from auth import auth_bp
from api import api_bp
import os

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # Initialize extensions
    db.init_app(app)
    
    # Initialize Flask-Login
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'Please log in to access this page.'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))
    
    # Initialize SocketIO for real-time collaboration
    socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')
    
    # Register blueprints
    app.register_blueprint(auth_bp)
    app.register_blueprint(api_bp)
    
    # Create upload directory
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    
    # Main routes
    @app.route('/')
    def splash():
        return render_template('splash.html')
    
    @app.route('/home')
    @login_required
    def homepage():
        # Get recent documents
        recent_docs = Document.query.filter_by(owner_id=current_user.id)\
            .order_by(Document.updated_at.desc()).limit(5).all()
        
        # Get document statistics
        total_docs = Document.query.filter_by(owner_id=current_user.id).count()
        total_words = sum(doc.word_count for doc in Document.query.filter_by(owner_id=current_user.id).all())
        
        return render_template('homepage.html', 
                             recent_docs=recent_docs,
                             total_docs=total_docs,
                             total_words=total_words)
    
    @app.route('/editor')
    @app.route('/editor/<int:doc_id>')
    @login_required
    def editor(doc_id=None):
        document = None
        if doc_id:
            document = Document.query.get_or_404(doc_id)
            # Check permissions
            if (document.owner_id != current_user.id and 
                not document.collaborations.filter_by(user_id=current_user.id).first() and
                not document.is_public):
                return redirect(url_for('homepage'))
        
        return render_template('editor.html', document=document)
    
    @app.route('/templates')
    @login_required
    def templates():
        template_docs = Document.query.filter_by(is_template=True).all()
        return render_template('templates.html', templates=template_docs)
    
    # SocketIO events for real-time collaboration
    @socketio.on('join_document')
    def on_join_document(data):
        doc_id = data['document_id']
        join_room(f'doc_{doc_id}')
        emit('user_joined', {
            'user_id': current_user.id,
            'username': current_user.get_full_name()
        }, room=f'doc_{doc_id}')
    
    @socketio.on('leave_document')
    def on_leave_document(data):
        doc_id = data['document_id']
        leave_room(f'doc_{doc_id}')
        emit('user_left', {
            'user_id': current_user.id,
            'username': current_user.get_full_name()
        }, room=f'doc_{doc_id}')
    
    @socketio.on('document_change')
    def on_document_change(data):
        doc_id = data['document_id']
        emit('document_updated', {
            'user_id': current_user.id,
            'username': current_user.get_full_name(),
            'changes': data['changes'],
            'timestamp': data.get('timestamp')
        }, room=f'doc_{doc_id}', include_self=False)
    
    @socketio.on('cursor_position')
    def on_cursor_position(data):
        doc_id = data['document_id']
        emit('cursor_update', {
            'user_id': current_user.id,
            'username': current_user.get_full_name(),
            'position': data['position']
        }, room=f'doc_{doc_id}', include_self=False)
    
    # Error handlers
    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return render_template('errors/500.html'), 500
    
    @app.errorhandler(403)
    def forbidden_error(error):
        return render_template('errors/403.html'), 403
    
    # Create database tables
    with app.app_context():
        db.create_all()
        
        # Create default templates if they don't exist
        create_default_templates()
    
    app.socketio = socketio
    return app

def create_default_templates():
    """Create default document templates"""
    templates = [
        {
            'title': 'Blank Document',
            'content': '',
            'content_json': {"ops": [{"insert": "\n"}]},
            'category': 'basic'
        },
        {
            'title': 'Business Letter',
            'content': '[Your Name]\n[Your Address]\n[City, State ZIP Code]\n[Email Address]\n[Phone Number]\n\n[Date]\n\n[Recipient Name]\n[Title]\n[Company Name]\n[Address]\n[City, State ZIP Code]\n\nDear [Recipient Name],\n\n[Letter content goes here...]\n\nSincerely,\n\n[Your Name]',
            'category': 'business'
        },
        {
            'title': 'Resume',
            'content': '[Your Name]\n[Your Address] | [Phone] | [Email]\n\nOBJECTIVE\n[Your career objective]\n\nEDUCATION\n[Degree] - [University Name] ([Year])\n\nEXPERIENCE\n[Job Title] - [Company Name] ([Dates])\n• [Achievement or responsibility]\n• [Achievement or responsibility]\n\nSKILLS\n• [Skill 1]\n• [Skill 2]\n• [Skill 3]',
            'category': 'personal'
        },
        {
            'title': 'Meeting Notes',
            'content': 'Meeting: [Meeting Title]\nDate: [Date]\nAttendees: [List of attendees]\n\nAGENDA\n1. [Agenda item 1]\n2. [Agenda item 2]\n3. [Agenda item 3]\n\nDISCUSSION POINTS\n• [Point 1]\n• [Point 2]\n• [Point 3]\n\nACTION ITEMS\n• [Action item 1] - [Assigned to] - [Due date]\n• [Action item 2] - [Assigned to] - [Due date]\n\nNEXT MEETING\nDate: [Next meeting date]\nTime: [Time]',
            'category': 'business'
        },
        {
            'title': 'Project Proposal',
            'content': 'PROJECT PROPOSAL\n\nProject Title: [Title]\nProposed by: [Your Name]\nDate: [Date]\n\nEXECUTIVE SUMMARY\n[Brief overview of the project]\n\nPROJECT DESCRIPTION\n[Detailed description of what the project entails]\n\nOBJECTIVES\n• [Objective 1]\n• [Objective 2]\n• [Objective 3]\n\nTIMELINE\n[Project timeline with key milestones]\n\nBUDGET\n[Budget breakdown]\n\nEXPECTED OUTCOMES\n[What you expect to achieve]\n\nCONCLUSION\n[Closing statement]',
            'category': 'business'
        }
    ]
    
    for template_data in templates:
        existing = Document.query.filter_by(title=template_data['title'], is_template=True).first()
        if not existing:
            template = Document(
                title=template_data['title'],
                content=template_data['content'],
                is_template=True,
                template_category=template_data['category'],
                owner_id=1  # Assuming admin user has ID 1
            )
            if 'content_json' in template_data:
                template.set_content_json(template_data['content_json'])
            else:
                template.set_content_json({"ops": [{"insert": template_data['content'] + "\n"}]})
            
            try:
                db.session.add(template)
                db.session.commit()
            except:
                db.session.rollback()

if __name__ == '__main__':
    app = create_app()
    app.socketio.run(app, debug=True, host='0.0.0.0', port=5000)
