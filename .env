# Triton Quill Environment Configuration

# Flask Configuration
SECRET_KEY=triton-quill-super-secret-key-2024-change-in-production
FLASK_ENV=development
FLASK_DEBUG=True

# Database Configuration
DATABASE_URL=sqlite:///triton_quill.db

# Email Configuration (Optional - for future features)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Application Settings
MAX_CONTENT_LENGTH=16777216
UPLOAD_FOLDER=static/uploads
DOCUMENTS_PER_PAGE=20

# Real-time Collaboration
SOCKETIO_ASYNC_MODE=threading
SOCKETIO_LOGGER=False
SOCKETIO_ENGINEIO_LOGGER=False
