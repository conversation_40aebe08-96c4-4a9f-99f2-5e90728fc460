#!/usr/bin/env python3
"""
Triton Quill - Professional Word Processor
A comprehensive word processing application with real-time collaboration,
document management, and professional features.

Created by Triton Software Labs
"""

from app import create_app

if __name__ == '__main__':
    app = create_app()
    print("🚀 Starting Triton Quill...")
    print("📝 Professional Word Processor")
    print("🌐 Access at: http://localhost:5000")
    print("💡 Features: Real-time collaboration, Templates, Export, and more!")

    # Run the application
    app.socketio.run(
        app,
        debug=True,
        host='0.0.0.0',
        port=5000,
        allow_unsafe_werkzeug=True
    )
