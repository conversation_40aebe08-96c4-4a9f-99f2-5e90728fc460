from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify
from flask_login import login_user, logout_user, login_required, current_user
from models import User, db
from datetime import datetime
import re

auth_bp = Blueprint('auth', __name__)

def validate_email(email):
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_password(password):
    """Password must be at least 8 characters with at least one letter and one number"""
    if len(password) < 8:
        return False, "Password must be at least 8 characters long"
    if not re.search(r'[A-Za-z]', password):
        return False, "Password must contain at least one letter"
    if not re.search(r'\d', password):
        return False, "Password must contain at least one number"
    return True, "Valid password"

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('main.homepage'))
    
    if request.method == 'POST':
        if request.is_json:
            data = request.get_json()
            email = data.get('email', '').strip().lower()
            password = data.get('password', '')
        else:
            email = request.form.get('email', '').strip().lower()
            password = request.form.get('password', '')
        
        if not email or not password:
            if request.is_json:
                return jsonify({'success': False, 'message': 'Email and password are required'})
            flash('Email and password are required', 'error')
            return render_template('login.html')
        
        user = User.query.filter_by(email=email).first()
        
        if user and user.check_password(password):
            if not user.is_active:
                if request.is_json:
                    return jsonify({'success': False, 'message': 'Account is deactivated'})
                flash('Your account has been deactivated', 'error')
                return render_template('login.html')
            
            user.last_login = datetime.utcnow()
            db.session.commit()
            login_user(user, remember=True)
            
            if request.is_json:
                return jsonify({
                    'success': True, 
                    'message': 'Login successful',
                    'redirect': url_for('main.homepage')
                })
            
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('main.homepage'))
        else:
            if request.is_json:
                return jsonify({'success': False, 'message': 'Invalid email or password'})
            flash('Invalid email or password', 'error')
    
    return render_template('login.html')

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    if current_user.is_authenticated:
        return redirect(url_for('main.homepage'))
    
    if request.method == 'POST':
        if request.is_json:
            data = request.get_json()
            first_name = data.get('first_name', '').strip()
            last_name = data.get('last_name', '').strip()
            username = data.get('username', '').strip().lower()
            email = data.get('email', '').strip().lower()
            password = data.get('password', '')
            confirm_password = data.get('confirm_password', '')
        else:
            first_name = request.form.get('first_name', '').strip()
            last_name = request.form.get('last_name', '').strip()
            username = request.form.get('username', '').strip().lower()
            email = request.form.get('email', '').strip().lower()
            password = request.form.get('password', '')
            confirm_password = request.form.get('confirm_password', '')
        
        # Validation
        errors = []
        
        if not all([first_name, last_name, username, email, password, confirm_password]):
            errors.append('All fields are required')
        
        if len(username) < 3:
            errors.append('Username must be at least 3 characters long')
        
        if not validate_email(email):
            errors.append('Please enter a valid email address')
        
        is_valid_password, password_message = validate_password(password)
        if not is_valid_password:
            errors.append(password_message)
        
        if password != confirm_password:
            errors.append('Passwords do not match')
        
        # Check if user already exists
        if User.query.filter_by(email=email).first():
            errors.append('Email address is already registered')
        
        if User.query.filter_by(username=username).first():
            errors.append('Username is already taken')
        
        if errors:
            if request.is_json:
                return jsonify({'success': False, 'message': '; '.join(errors)})
            for error in errors:
                flash(error, 'error')
            return render_template('login.html')
        
        # Create new user
        try:
            user = User(
                first_name=first_name,
                last_name=last_name,
                username=username,
                email=email
            )
            user.set_password(password)
            
            db.session.add(user)
            db.session.commit()
            
            login_user(user, remember=True)
            
            if request.is_json:
                return jsonify({
                    'success': True, 
                    'message': 'Registration successful',
                    'redirect': url_for('main.homepage')
                })
            
            flash('Registration successful! Welcome to Triton Quill!', 'success')
            return redirect(url_for('main.homepage'))
            
        except Exception as e:
            db.session.rollback()
            if request.is_json:
                return jsonify({'success': False, 'message': 'Registration failed. Please try again.'})
            flash('Registration failed. Please try again.', 'error')
    
    return render_template('login.html')

@auth_bp.route('/logout')
@login_required
def logout():
    logout_user()
    flash('You have been logged out successfully', 'info')
    return redirect(url_for('main.splash'))

@auth_bp.route('/profile')
@login_required
def profile():
    return render_template('profile.html', user=current_user)

@auth_bp.route('/update_profile', methods=['POST'])
@login_required
def update_profile():
    if request.is_json:
        data = request.get_json()
    else:
        data = request.form
    
    first_name = data.get('first_name', '').strip()
    last_name = data.get('last_name', '').strip()
    
    if not first_name or not last_name:
        if request.is_json:
            return jsonify({'success': False, 'message': 'First name and last name are required'})
        flash('First name and last name are required', 'error')
        return redirect(url_for('auth.profile'))
    
    try:
        current_user.first_name = first_name
        current_user.last_name = last_name
        db.session.commit()
        
        if request.is_json:
            return jsonify({'success': True, 'message': 'Profile updated successfully'})
        flash('Profile updated successfully', 'success')
        
    except Exception as e:
        db.session.rollback()
        if request.is_json:
            return jsonify({'success': False, 'message': 'Failed to update profile'})
        flash('Failed to update profile', 'error')
    
    return redirect(url_for('auth.profile'))

@auth_bp.route('/change_password', methods=['POST'])
@login_required
def change_password():
    if request.is_json:
        data = request.get_json()
    else:
        data = request.form
    
    current_password = data.get('current_password', '')
    new_password = data.get('new_password', '')
    confirm_password = data.get('confirm_password', '')
    
    if not all([current_password, new_password, confirm_password]):
        if request.is_json:
            return jsonify({'success': False, 'message': 'All password fields are required'})
        flash('All password fields are required', 'error')
        return redirect(url_for('auth.profile'))
    
    if not current_user.check_password(current_password):
        if request.is_json:
            return jsonify({'success': False, 'message': 'Current password is incorrect'})
        flash('Current password is incorrect', 'error')
        return redirect(url_for('auth.profile'))
    
    is_valid, message = validate_password(new_password)
    if not is_valid:
        if request.is_json:
            return jsonify({'success': False, 'message': message})
        flash(message, 'error')
        return redirect(url_for('auth.profile'))
    
    if new_password != confirm_password:
        if request.is_json:
            return jsonify({'success': False, 'message': 'New passwords do not match'})
        flash('New passwords do not match', 'error')
        return redirect(url_for('auth.profile'))
    
    try:
        current_user.set_password(new_password)
        db.session.commit()
        
        if request.is_json:
            return jsonify({'success': True, 'message': 'Password changed successfully'})
        flash('Password changed successfully', 'success')
        
    except Exception as e:
        db.session.rollback()
        if request.is_json:
            return jsonify({'success': False, 'message': 'Failed to change password'})
        flash('Failed to change password', 'error')
    
    return redirect(url_for('auth.profile'))
