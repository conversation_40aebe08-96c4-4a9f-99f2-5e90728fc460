// Triton Quill Editor - Main JavaScript File

// Initialize Quill editor
function initializeEditor() {
    const toolbarOptions = [
        ['bold', 'italic', 'underline', 'strike'],
        ['blockquote', 'code-block'],
        [{ 'header': 1 }, { 'header': 2 }],
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
        [{ 'script': 'sub'}, { 'script': 'super' }],
        [{ 'indent': '-1'}, { 'indent': '+1' }],
        [{ 'direction': 'rtl' }],
        [{ 'size': ['small', false, 'large', 'huge'] }],
        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
        [{ 'color': [] }, { 'background': [] }],
        [{ 'font': [] }],
        [{ 'align': [] }],
        ['clean'],
        ['link', 'image', 'video'],
        ['table']
    ];

    quill = new Quill('#editor', {
        theme: 'snow',
        modules: {
            toolbar: false, // We'll use custom toolbar
            history: {
                delay: 1000,
                maxStack: 100,
                userOnly: true
            },
            table: true
        },
        placeholder: 'Start writing your document...',
        formats: [
            'bold', 'italic', 'underline', 'strike', 'blockquote', 'code-block',
            'header', 'list', 'script', 'indent', 'direction', 'size',
            'color', 'background', 'font', 'align', 'clean', 'link', 'image', 'video'
        ]
    });

    // Set up event listeners
    quill.on('text-change', function(delta, oldDelta, source) {
        if (source === 'user') {
            updateWordCount();
            updateDocumentOutline();
            scheduleAutoSave();
            
            // Emit changes for real-time collaboration
            if (socket && documentId) {
                socket.emit('document_change', {
                    document_id: documentId,
                    changes: delta,
                    timestamp: Date.now()
                });
            }
        }
    });

    quill.on('selection-change', function(range, oldRange, source) {
        if (range) {
            if (range.length === 0) {
                hideFloatingToolbar();
            } else {
                showFloatingToolbar(range);
            }
            
            // Emit cursor position for collaboration
            if (socket && documentId && source === 'user') {
                socket.emit('cursor_position', {
                    document_id: documentId,
                    position: range
                });
            }
        }
    });

    // Initialize word count
    updateWordCount();
}

// Initialize Socket.IO for real-time collaboration
function initializeSocketIO() {
    if (documentId) {
        socket = io();
        
        socket.on('connect', function() {
            socket.emit('join_document', { document_id: documentId });
        });
        
        socket.on('document_updated', function(data) {
            // Apply changes from other users
            if (data.changes) {
                quill.updateContents(data.changes, 'api');
            }
        });
        
        socket.on('cursor_update', function(data) {
            updateCollaboratorCursor(data.user_id, data.username, data.position);
        });
        
        socket.on('user_joined', function(data) {
            showNotification(`${data.username} joined the document`, 'info');
        });
        
        socket.on('user_left', function(data) {
            removeCollaboratorCursor(data.user_id);
        });
    }
}

// Keyboard shortcuts
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey || e.metaKey) {
            switch(e.key) {
                case 's':
                    e.preventDefault();
                    saveDocument();
                    break;
                case 'b':
                    e.preventDefault();
                    formatText('bold');
                    break;
                case 'i':
                    e.preventDefault();
                    formatText('italic');
                    break;
                case 'u':
                    e.preventDefault();
                    formatText('underline');
                    break;
                case 'f':
                    e.preventDefault();
                    findReplace();
                    break;
                case 'z':
                    if (e.shiftKey) {
                        e.preventDefault();
                        quill.history.redo();
                    } else {
                        e.preventDefault();
                        quill.history.undo();
                    }
                    break;
            }
        }
    });
}

// Load document content
function loadDocument() {
    if (documentId) {
        fetch(`/api/documents/${documentId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const doc = data.document;
                    document.getElementById('documentTitle').value = doc.title;
                    
                    if (doc.content_json && doc.content_json.ops) {
                        quill.setContents(doc.content_json);
                    } else if (doc.content) {
                        quill.setText(doc.content);
                    }
                    
                    updateWordCount();
                    updateDocumentOutline();
                    lastSaveTime = new Date(doc.updated_at);
                    updateLastSavedTime();
                }
            })
            .catch(error => {
                console.error('Error loading document:', error);
                showNotification('Failed to load document', 'error');
            });
    }
}

// Text formatting functions
function formatText(format) {
    const range = quill.getSelection();
    if (range) {
        const currentFormat = quill.getFormat(range);
        quill.format(format, !currentFormat[format]);
        updateToolbarState();
    }
}

function changeFontFamily(font) {
    const range = quill.getSelection();
    if (range) {
        quill.format('font', font);
    }
}

function changeFontSize(size) {
    const range = quill.getSelection();
    if (range) {
        quill.format('size', size + 'px');
    }
}

function alignText(alignment) {
    const range = quill.getSelection();
    if (range) {
        quill.format('align', alignment);
    }
}

function insertList(type) {
    const range = quill.getSelection();
    if (range) {
        quill.format('list', type);
    }
}

function applyStyle(style) {
    const range = quill.getSelection();
    if (range) {
        switch(style) {
            case 'heading1':
                quill.format('header', 1);
                break;
            case 'heading2':
                quill.format('header', 2);
                break;
            case 'heading3':
                quill.format('header', 3);
                break;
            case 'normal':
                quill.format('header', false);
                break;
            case 'quote':
                quill.format('blockquote', true);
                break;
        }
    }
}

// Insert functions
function insertTable() {
    const rows = prompt('Number of rows:', '3');
    const cols = prompt('Number of columns:', '3');
    
    if (rows && cols) {
        let tableHTML = '<table border="1" style="border-collapse: collapse; width: 100%;">';
        for (let i = 0; i < parseInt(rows); i++) {
            tableHTML += '<tr>';
            for (let j = 0; j < parseInt(cols); j++) {
                tableHTML += '<td style="padding: 8px; border: 1px solid #ddd;">&nbsp;</td>';
            }
            tableHTML += '</tr>';
        }
        tableHTML += '</table>';
        
        const range = quill.getSelection();
        if (range) {
            quill.clipboard.dangerouslyPasteHTML(range.index, tableHTML);
        }
    }
}

function insertImage() {
    document.getElementById('imageUpload').click();
}

function handleImageUpload(input) {
    const file = input.files[0];
    if (file) {
        const formData = new FormData();
        formData.append('image', file);
        
        fetch('/api/upload-image', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const range = quill.getSelection();
                if (range) {
                    quill.insertEmbed(range.index, 'image', data.url);
                }
            } else {
                showNotification('Failed to upload image', 'error');
            }
        })
        .catch(error => {
            showNotification('Failed to upload image', 'error');
        });
    }
    input.value = '';
}

function insertLink() {
    const range = quill.getSelection();
    if (range) {
        const url = prompt('Enter URL:');
        if (url) {
            quill.format('link', url);
        }
    }
}

// Document operations
function saveDocument() {
    const title = document.getElementById('documentTitle').value.trim() || 'Untitled Document';
    const content = quill.getText();
    const contentJson = quill.getContents();
    
    const data = {
        title: title,
        content: content,
        content_json: contentJson
    };
    
    const url = documentId ? `/api/documents/${documentId}` : '/api/documents';
    const method = documentId ? 'PUT' : 'POST';
    
    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (!documentId) {
                documentId = data.document.id;
                // Update URL without page reload
                window.history.replaceState({}, '', `/editor/${documentId}`);
                // Initialize socket connection for new document
                initializeSocketIO();
            }
            
            lastSaveTime = new Date();
            updateLastSavedTime();
            showSaveIndicator();
            showNotification('Document saved successfully', 'success');
        } else {
            showNotification('Failed to save document: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showNotification('Failed to save document', 'error');
    });
}

function scheduleAutoSave() {
    clearTimeout(saveTimeout);
    saveTimeout = setTimeout(() => {
        if (documentId) {
            saveDocument();
        }
    }, 5000); // Auto-save after 5 seconds of inactivity
}

function exportDocument(format) {
    if (!documentId) {
        showNotification('Please save the document first', 'warning');
        return;
    }
    
    window.open(`/api/documents/${documentId}/export/${format}`, '_blank');
}

// UI functions
function updateWordCount() {
    const text = quill.getText();
    const words = text.trim().split(/\s+/).filter(word => word.length > 0).length;
    const chars = text.length;
    
    document.getElementById('wordCount').textContent = `${words} words`;
    document.getElementById('charCount').textContent = `${chars} characters`;
    document.getElementById('wordCountDisplay').textContent = `${words} words`;
}

function updateLastSavedTime() {
    if (lastSaveTime) {
        const timeStr = lastSaveTime.toLocaleTimeString();
        document.getElementById('lastSaved').textContent = `Last saved: ${timeStr}`;
    }
}

function showSaveIndicator() {
    const indicator = document.getElementById('saveIndicator');
    indicator.classList.add('show');
    setTimeout(() => {
        indicator.classList.remove('show');
    }, 2000);
}

function updateDocumentOutline() {
    const contents = quill.getContents();
    const outline = [];
    
    contents.ops.forEach((op, index) => {
        if (op.attributes && op.attributes.header) {
            const level = op.attributes.header;
            const text = op.insert.trim();
            if (text) {
                outline.push({ level, text, index });
            }
        }
    });
    
    const outlineContainer = document.getElementById('documentOutline');
    if (outline.length > 0) {
        let outlineHTML = '<ul class="list-unstyled">';
        outline.forEach(item => {
            const indent = (item.level - 1) * 20;
            outlineHTML += `
                <li style="margin-left: ${indent}px; margin-bottom: 0.5rem;">
                    <a href="#" onclick="scrollToHeading(${item.index})" 
                       class="text-decoration-none text-primary">
                        <small>H${item.level}</small> ${item.text}
                    </a>
                </li>
            `;
        });
        outlineHTML += '</ul>';
        outlineContainer.innerHTML = outlineHTML;
    } else {
        outlineContainer.innerHTML = '<p class="text-muted">Start writing to see your document outline here.</p>';
    }
}

function scrollToHeading(index) {
    quill.setSelection(index, 0);
    quill.scrollIntoView();
}

// Toolbar functions
function updateToolbarState() {
    const range = quill.getSelection();
    if (range) {
        const format = quill.getFormat(range);
        
        // Update button states
        document.querySelectorAll('.toolbar-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        if (format.bold) document.querySelector('[onclick="formatText(\'bold\')"]').classList.add('active');
        if (format.italic) document.querySelector('[onclick="formatText(\'italic\')"]').classList.add('active');
        if (format.underline) document.querySelector('[onclick="formatText(\'underline\')"]').classList.add('active');
        if (format.strike) document.querySelector('[onclick="formatText(\'strike\')"]').classList.add('active');
    }
}

function showFloatingToolbar(range) {
    const bounds = quill.getBounds(range);
    const toolbar = document.getElementById('floatingToolbar');
    
    toolbar.style.left = bounds.left + 'px';
    toolbar.style.top = (bounds.top - 50) + 'px';
    toolbar.style.display = 'block';
}

function hideFloatingToolbar() {
    document.getElementById('floatingToolbar').style.display = 'none';
}

// Sidebar functions
function toggleSidebar() {
    const sidebar = document.getElementById('editorSidebar');
    sidebar.classList.toggle('collapsed');
}

function switchSidebarTab(tabName) {
    // Update tab buttons
    document.querySelectorAll('.sidebar-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // Update tab content
    document.querySelectorAll('.sidebar-tab-content').forEach(content => {
        content.style.display = 'none';
    });
    document.getElementById(tabName + 'Tab').style.display = 'block';
}

// Comments functions
function toggleComments() {
    const panel = document.getElementById('commentsPanel');
    panel.classList.toggle('hidden');
}

function addComment() {
    const range = quill.getSelection();
    if (range && range.length > 0) {
        const text = prompt('Add your comment:');
        if (text) {
            // This would integrate with the comments API
            showNotification('Comment functionality coming soon!', 'info');
        }
    } else {
        showNotification('Please select text to comment on', 'warning');
    }
}

// Collaboration functions
function updateCollaboratorCursor(userId, username, position) {
    const cursorsContainer = document.getElementById('collaborationCursors');
    let cursor = cursorsContainer.querySelector(`[data-user-id="${userId}"]`);
    
    if (!cursor) {
        cursor = document.createElement('div');
        cursor.className = 'user-cursor';
        cursor.setAttribute('data-user-id', userId);
        cursor.setAttribute('data-user', username);
        cursorsContainer.appendChild(cursor);
    }
    
    if (position) {
        const bounds = quill.getBounds(position);
        cursor.style.left = bounds.left + 'px';
        cursor.style.top = bounds.top + 'px';
        cursor.style.display = 'block';
    } else {
        cursor.style.display = 'none';
    }
}

function removeCollaboratorCursor(userId) {
    const cursor = document.querySelector(`[data-user-id="${userId}"]`);
    if (cursor) {
        cursor.remove();
    }
}

// Utility functions
function findReplace() {
    showNotification('Find & Replace functionality coming soon!', 'info');
}

function spellCheck() {
    showNotification('Spell check functionality coming soon!', 'info');
}

function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen();
    } else {
        document.exitFullscreen();
    }
}

function showNotification(message, type = 'info') {
    if (window.TritonQuill && window.TritonQuill.showAlert) {
        window.TritonQuill.showAlert(message, type);
    } else {
        alert(message);
    }
}

// Cleanup on page unload
window.addEventListener('beforeunload', function(e) {
    if (socket && documentId) {
        socket.emit('leave_document', { document_id: documentId });
    }
});
